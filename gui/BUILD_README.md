# Version-Craft GUI 打包说明

## 🚀 一键打包脚本

本项目提供了多种一键打包脚本，可以将 GUI 应用打包成独立的 Windows 可执行文件。

## 📦 打包方式

### 方式一：Windows 批处理文件（推荐）
```bash
# 双击运行或在命令行执行
build-exe.bat
```

### 方式二：PowerShell 脚本
```powershell
# 在 PowerShell 中执行
.\build-exe.ps1
```

### 方式三：Node.js 脚本（跨平台）
```bash
# 使用 Node.js 执行
node build-exe.js

# 或使用 npm 脚本
npm run pack-exe
```

### 方式四：手动执行
```bash
# 1. 安装依赖
npm install

# 2. 构建所有组件
npm run build

# 3. 打包成 Windows 可执行文件
npm run dist:win
```

## 📁 输出文件

打包完成后，可执行文件将生成在 `release/` 目录中：

```
release/
├── Version-Craft GUI Setup 1.0.0.exe  # 安装程序
└── win-unpacked/                       # 解压版本
    ├── Version-Craft GUI.exe          # 主程序
    ├── resources/
    └── ...
```

## ⚙️ 打包配置

打包配置位于 `package.json` 的 `build` 字段中：

```json
{
  "build": {
    "appId": "com.versioncraft.gui",
    "productName": "Version-Craft GUI",
    "directories": {
      "output": "release"
    },
    "files": [
      "dist/**/*",
      "package.json"
    ],
    "win": {
      "target": [
        {
          "target": "nsis",
          "arch": ["x64"]
        }
      ]
    }
  }
}
```

## 🔧 自定义配置

### 修改应用信息
在 `package.json` 中修改：
- `name`: 应用名称
- `version`: 版本号
- `description`: 应用描述
- `author`: 作者信息

### 修改打包配置
在 `package.json` 的 `build` 字段中修改：
- `appId`: 应用 ID
- `productName`: 产品名称
- `directories.output`: 输出目录
- `win.target`: Windows 打包目标

### 添加应用图标
1. 准备 `.ico` 格式的图标文件
2. 在 `build` 配置中添加：
```json
{
  "build": {
    "win": {
      "icon": "path/to/icon.ico"
    }
  }
}
```

## 🛠️ 故障排除

### 常见问题

1. **Node.js 未安装**
   - 下载并安装 [Node.js](https://nodejs.org/)

2. **依赖安装失败**
   ```bash
   # 清理缓存后重新安装
   npm cache clean --force
   npm install
   ```

3. **构建失败**
   ```bash
   # 清理构建文件后重新构建
   npm run clean
   npm run build
   ```

4. **打包失败**
   - 检查 `electron-builder` 是否正确安装
   - 确保所有构建文件都已生成
   - 检查网络连接（可能需要下载 Electron 二进制文件）

### 环境要求

- Node.js 16.x 或更高版本
- npm 8.x 或更高版本
- Windows 10 或更高版本（用于 Windows 打包）

## 📝 注意事项

1. 首次打包可能需要下载 Electron 二进制文件，请确保网络连接正常
2. 打包过程可能需要几分钟时间，请耐心等待
3. 生成的可执行文件包含完整的 Node.js 运行时，文件较大是正常现象
4. 建议在打包前先测试开发版本是否正常运行

## 🎯 快速开始

1. 确保已安装 Node.js
2. 在项目根目录双击 `build-exe.bat`
3. 等待打包完成
4. 在 `release/` 目录中找到生成的可执行文件

就这么简单！🎉
