@echo off
echo ===== DEBUG BUILD SCRIPT =====
echo Current directory: %CD%
echo.

echo Checking Node.js installation...
node --version
if errorlevel 1 (
    echo Node.js not found!
    pause
    exit /b 1
)

echo Checking npm installation...
npm --version
if errorlevel 1 (
    echo npm not found!
    pause
    exit /b 1
)

echo Checking package.json...
if not exist "package.json" (
    echo package.json not found in current directory!
    echo Please navigate to the gui directory first.
    pause
    exit /b 1
)

echo.
echo ===== STARTING BUILD PROCESS =====
echo.

echo [1/5] Installing dependencies...
npm install
echo npm install exit code: %errorlevel%
if errorlevel 1 goto :error

echo.
echo [2/5] Building main process...
npm run build:main
echo build:main exit code: %errorlevel%
if errorlevel 1 goto :error

echo.
echo [3/5] Building preload script...
npm run build:preload
echo build:preload exit code: %errorlevel%
if errorlevel 1 goto :error

echo.
echo [4/5] Building renderer process...
npm run build:renderer
echo build:renderer exit code: %errorlevel%
if errorlevel 1 goto :error

echo.
echo [5/5] Packaging application...
npm run dist:win
echo dist:win exit code: %errorlevel%
if errorlevel 1 goto :error

echo.
echo ===== BUILD SUCCESSFUL =====
echo.
if exist "release" (
    echo Release folder contents:
    dir "release" /b
    echo.
    echo Opening release folder...
    start "" "release"
) else (
    echo WARNING: Release folder not found!
)

echo.
echo Build completed successfully!
pause
exit /b 0

:error
echo.
echo ===== BUILD FAILED =====
echo Last command failed with exit code: %errorlevel%
echo.
pause
exit /b 1
