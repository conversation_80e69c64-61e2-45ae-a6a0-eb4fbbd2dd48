@echo off
setlocal enabledelayedexpansion
title Version-Craft GUI Build Script

echo.
echo ========================================
echo    Version-Craft GUI Build Script
echo ========================================
echo.

:: 检查 Node.js 是否安装
echo [INFO] Checking Node.js...
node --version >nul 2>&1
if !errorlevel! neq 0 (
    echo [ERROR] Node.js not found. Please install Node.js first.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

:: 检查 npm 是否可用
echo [INFO] Checking npm...
npm --version >nul 2>&1
if !errorlevel! neq 0 (
    echo [ERROR] npm not available.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo [SUCCESS] Environment check passed.
echo.

:: 清理之前的构建文件
echo [INFO] Cleaning previous build files...
if exist "dist" (
    rmdir /s /q "dist" 2>nul
    if exist "dist" echo [WARNING] Could not remove dist folder completely
)
if exist "release" (
    rmdir /s /q "release" 2>nul
    if exist "release" echo [WARNING] Could not remove release folder completely
)
echo [SUCCESS] Cleanup completed.
echo.

:: 安装依赖
echo [INFO] Installing dependencies...
call npm install
if !errorlevel! neq 0 (
    echo [ERROR] Dependencies installation failed.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo [SUCCESS] Dependencies installed.
echo.

:: 构建主进程
echo [INFO] Building main process...
call npm run build:main
if !errorlevel! neq 0 (
    echo [ERROR] Main process build failed.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo [SUCCESS] Main process build completed.
echo.

:: 构建预加载脚本
echo [INFO] Building preload script...
call npm run build:preload
if !errorlevel! neq 0 (
    echo [ERROR] Preload script build failed.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo [SUCCESS] Preload script build completed.
echo.

:: 构建渲染进程
echo [INFO] Building renderer process...
call npm run build:renderer
if !errorlevel! neq 0 (
    echo [ERROR] Renderer process build failed.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo [SUCCESS] Renderer process build completed.
echo.

:: 打包成 exe
echo [INFO] Packaging to Windows executable...
call npm run dist:win
if !errorlevel! neq 0 (
    echo [ERROR] Packaging failed.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo.
echo ========================================
echo           BUILD COMPLETED!
echo ========================================
echo.
echo Output directory: release/
echo Executable files have been generated.
echo.

:: 打开输出目录
if exist "release" (
    echo [INFO] Opening output directory...
    start "" "release" 2>nul
    if !errorlevel! neq 0 (
        echo [WARNING] Could not open directory automatically.
        echo Please check the release/ folder manually.
    )
) else (
    echo [WARNING] Release directory not found.
)

echo.
echo Press any key to exit...
pause >nul
