#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function colorLog(message, color = 'white') {
  console.log(colors[color] + message + colors.reset);
}

function execCommand(command, description) {
  try {
    colorLog(`🔨 ${description}...`, 'yellow');
    execSync(command, { stdio: 'inherit' });
    colorLog(`✅ ${description}完成`, 'green');
    return true;
  } catch (error) {
    colorLog(`❌ ${description}失败`, 'red');
    console.error(error.message);
    return false;
  }
}

function checkCommand(command, name) {
  try {
    execSync(`${command} --version`, { stdio: 'pipe' });
    return true;
  } catch (error) {
    colorLog(`❌ 错误: 未检测到 ${name}，请先安装 ${name}`, 'red');
    return false;
  }
}

function main() {
  console.log('');
  colorLog('========================================', 'cyan');
  colorLog('    Version-Craft GUI 一键打包脚本', 'cyan');
  colorLog('========================================', 'cyan');
  console.log('');

  // 检查环境
  if (!checkCommand('node', 'Node.js')) {
    process.exit(1);
  }
  
  if (!checkCommand('npm', 'npm')) {
    process.exit(1);
  }

  colorLog('✅ 环境检查通过', 'green');
  console.log('');

  // 清理之前的构建文件
  colorLog('🧹 清理之前的构建文件...', 'yellow');
  try {
    if (fs.existsSync('dist')) {
      fs.rmSync('dist', { recursive: true, force: true });
    }
    if (fs.existsSync('release')) {
      fs.rmSync('release', { recursive: true, force: true });
    }
    colorLog('✅ 清理完成', 'green');
  } catch (error) {
    colorLog('⚠️ 清理时出现警告，继续执行...', 'yellow');
  }
  console.log('');

  // 执行构建步骤
  const steps = [
    { cmd: 'npm install', desc: '重新安装依赖（修复 electron 位置）' },
    { cmd: 'npm run build:main', desc: '构建主进程' },
    { cmd: 'npm run build:preload', desc: '构建预加载脚本' },
    { cmd: 'npm run build:renderer', desc: '构建渲染进程' },
    { cmd: 'npm run dist:win', desc: '打包成 Windows 可执行文件' }
  ];

  for (const step of steps) {
    if (!execCommand(step.cmd, step.desc)) {
      process.exit(1);
    }
    console.log('');
  }

  // 完成
  console.log('');
  colorLog('========================================', 'green');
  colorLog('           🎉 打包完成！', 'green');
  colorLog('========================================', 'green');
  console.log('');
  colorLog('📁 输出目录: release/', 'cyan');
  colorLog('🚀 可执行文件已生成，请查看 release 文件夹', 'cyan');
  console.log('');

  // 尝试打开输出目录
  if (fs.existsSync('release')) {
    colorLog('📂 输出目录已生成', 'green');
    
    // 根据平台打开文件夹
    const platform = process.platform;
    try {
      if (platform === 'win32') {
        execSync('start "" "release"', { stdio: 'ignore' });
      } else if (platform === 'darwin') {
        execSync('open release', { stdio: 'ignore' });
      } else {
        execSync('xdg-open release', { stdio: 'ignore' });
      }
      colorLog('📂 已打开输出目录', 'green');
    } catch (error) {
      colorLog('⚠️ 无法自动打开目录，请手动查看 release 文件夹', 'yellow');
    }
  }
}

// 运行主函数
main();
