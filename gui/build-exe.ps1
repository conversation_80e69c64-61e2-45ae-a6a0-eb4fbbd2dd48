# Version-Craft GUI 一键打包脚本 (PowerShell)
# 设置控制台编码为 UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Version-Craft GUI 一键打包脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查 Node.js 是否安装
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Node.js 版本: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Host "❌ 错误: 未检测到 Node.js，请先安装 Node.js" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit 1
}

# 检查 npm 是否可用
try {
    $npmVersion = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ npm 版本: $npmVersion" -ForegroundColor Green
    } else {
        throw "npm not found"
    }
} catch {
    Write-Host "❌ 错误: npm 不可用" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit 1
}

Write-Host ""

# 清理之前的构建文件
Write-Host "🧹 清理之前的构建文件..." -ForegroundColor Yellow
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
if (Test-Path "release") { Remove-Item -Recurse -Force "release" }
Write-Host "✅ 清理完成" -ForegroundColor Green
Write-Host ""

# 安装依赖
Write-Host "📦 检查并安装依赖..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 依赖安装失败" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit 1
}
Write-Host "✅ 依赖安装完成" -ForegroundColor Green
Write-Host ""

# 构建主进程
Write-Host "🔨 构建主进程..." -ForegroundColor Yellow
npm run build:main
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 主进程构建失败" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit 1
}
Write-Host "✅ 主进程构建完成" -ForegroundColor Green
Write-Host ""

# 构建预加载脚本
Write-Host "🔨 构建预加载脚本..." -ForegroundColor Yellow
npm run build:preload
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 预加载脚本构建失败" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit 1
}
Write-Host "✅ 预加载脚本构建完成" -ForegroundColor Green
Write-Host ""

# 构建渲染进程
Write-Host "🔨 构建渲染进程..." -ForegroundColor Yellow
npm run build:renderer
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 渲染进程构建失败" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit 1
}
Write-Host "✅ 渲染进程构建完成" -ForegroundColor Green
Write-Host ""

# 打包成 exe
Write-Host "📦 打包成 Windows 可执行文件..." -ForegroundColor Yellow
npm run dist:win
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 打包失败" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "           🎉 打包完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "📁 输出目录: release/" -ForegroundColor Cyan
Write-Host "🚀 可执行文件已生成，请查看 release 文件夹" -ForegroundColor Cyan
Write-Host ""

# 打开输出目录
if (Test-Path "release") {
    Write-Host "📂 正在打开输出目录..." -ForegroundColor Yellow
    Start-Process "release"
}

Read-Host "按 Enter 键退出"
