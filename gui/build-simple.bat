@echo off
echo Starting Version-Craft GUI build process...
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo ERROR: package.json not found. Please run this script from the gui directory.
    pause
    exit /b 1
)

REM Install dependencies
echo Step 1/5: Installing dependencies...
npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

REM Build main process
echo Step 2/5: Building main process...
npm run build:main
if errorlevel 1 (
    echo ERROR: Failed to build main process
    pause
    exit /b 1
)

REM Build preload script
echo Step 3/5: Building preload script...
npm run build:preload
if errorlevel 1 (
    echo ERROR: Failed to build preload script
    pause
    exit /b 1
)

REM Build renderer process
echo Step 4/5: Building renderer process...
npm run build:renderer
if errorlevel 1 (
    echo ERROR: Failed to build renderer process
    pause
    exit /b 1
)

REM Package the application
echo Step 5/5: Packaging application...
npm run dist:win
if errorlevel 1 (
    echo ERROR: Failed to package application
    pause
    exit /b 1
)

echo.
echo SUCCESS: Build completed!
echo Check the release/ folder for the executable files.
echo.

REM Try to open the release folder
if exist "release" (
    explorer "release"
)

pause
