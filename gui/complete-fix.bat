@echo off
echo ===== Complete Fix and Build Script =====
echo.

echo [STEP 1] Checking environment...
node --version
if errorlevel 1 (
    echo ERROR: Node.js not found
    pause
    exit /b 1
)

npm --version
if errorlevel 1 (
    echo ERROR: npm not found
    pause
    exit /b 1
)

echo [STEP 2] Complete cleanup...
echo Removing node_modules...
if exist "node_modules" rmdir /s /q "node_modules"

echo Removing package-lock.json...
if exist "package-lock.json" del "package-lock.json"

echo Removing yarn.lock if exists...
if exist "yarn.lock" del "yarn.lock"

echo Removing build directories...
if exist "dist" rmdir /s /q "dist"
if exist "release" rmdir /s /q "release"

echo [STEP 3] Clearing npm cache...
npm cache clean --force

echo [STEP 4] Installing dependencies with verbose output...
npm install --verbose
if errorlevel 1 (
    echo ERROR: npm install failed
    echo.
    echo Trying alternative installation methods...
    echo.
    
    echo Trying with --legacy-peer-deps...
    npm install --legacy-peer-deps
    if errorlevel 1 (
        echo ERROR: Installation failed with all methods
        pause
        exit /b 1
    )
)

echo [STEP 5] Verifying electron installation...
if not exist "node_modules\electron" (
    echo WARNING: electron not found in node_modules
    echo Installing electron manually...
    npm install electron@^25.9.0 --save-dev
    if errorlevel 1 (
        echo ERROR: Manual electron installation failed
        pause
        exit /b 1
    )
)

echo [STEP 6] Verifying electron-builder installation...
if not exist "node_modules\electron-builder" (
    echo WARNING: electron-builder not found
    echo Installing electron-builder manually...
    npm install electron-builder@^24.6.4 --save-dev
    if errorlevel 1 (
        echo ERROR: Manual electron-builder installation failed
        pause
        exit /b 1
    )
)

echo [STEP 7] Testing electron version...
npx electron --version
if errorlevel 1 (
    echo ERROR: electron not working properly
    pause
    exit /b 1
)

echo [STEP 8] Building main process...
npm run build:main
if errorlevel 1 (
    echo ERROR: Main process build failed
    pause
    exit /b 1
)

echo [STEP 9] Building preload script...
npm run build:preload
if errorlevel 1 (
    echo ERROR: Preload build failed
    pause
    exit /b 1
)

echo [STEP 10] Building renderer process...
npm run build:renderer
if errorlevel 1 (
    echo ERROR: Renderer build failed
    pause
    exit /b 1
)

echo [STEP 11] Final packaging...
echo This may take several minutes for first-time build...
npm run dist:win
if errorlevel 1 (
    echo ERROR: Packaging failed
    echo.
    echo Trying with electron-builder directly...
    npx electron-builder --win
    if errorlevel 1 (
        echo ERROR: Direct electron-builder also failed
        pause
        exit /b 1
    )
)

echo.
echo ===== SUCCESS! =====
echo.
echo Build completed successfully!
echo Check the release/ folder for your executable files.
echo.

if exist "release" (
    echo Opening release folder...
    start "" "release"
    echo.
    echo Files in release folder:
    dir "release" /b
)

echo.
pause
