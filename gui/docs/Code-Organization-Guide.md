# 📁 Version-Craft GUI 代码组织规范

## 📋 概述

本文档详细说明了 Version-Craft GUI 项目的代码组织规范，特别是事件总线系统的文件放置原则。正确的代码组织能够提高项目的可维护性、可扩展性和团队协作效率。

## 🏗️ 目录结构设计原则

### **核心原则**
1. **环境隔离** - 主进程和渲染进程代码分离
2. **功能内聚** - 相关功能放在同一目录
3. **依赖清晰** - 避免循环依赖和不合理的跨目录引用
4. **编译优化** - 支持不同的 TypeScript 编译配置

### **目录职责划分**

```
gui/src/
├── main/                    # 主进程专用代码
│   ├── events/             # 主进程事件系统
│   ├── services/           # 主进程业务服务
│   ├── ipc/               # IPC 处理器
│   └── index.ts           # 主进程入口
├── renderer/               # 渲染进程专用代码
│   ├── components/        # Vue 组件
│   ├── stores/           # Pinia 状态管理
│   ├── composables/      # Vue Composables
│   └── App.vue           # 渲染进程入口
├── shared/                # 跨进程共享代码
│   ├── types/            # 类型定义
│   ├── constants/        # 常量定义
│   ├── events/           # 跨环境事件系统
│   ├── services/         # 共享服务
│   └── api/              # API 客户端
└── preload/               # Preload 脚本
    └── index.ts
```

## 🎯 事件系统文件放置案例

### **案例背景**
在设计事件总线系统时，我们面临一个关键问题：`MainEventBus.ts` 应该放在 `main/events` 还是 `shared/events` 目录？

### **分析过程**

#### **选项1: 放在 `shared/events`**
```
❌ 问题分析：
- MainEventBus 依赖 Node.js 的 EventEmitter
- 需要访问 BrowserWindow 等主进程 API
- 在渲染进程编译时会出现依赖错误
- 违反了环境隔离原则
```

#### **选项2: 放在 `main/events`** ✅
```
✅ 优势分析：
- 符合环境隔离原则
- 可以安全使用主进程 API
- 编译配置清晰
- 依赖关系合理
```

### **最终决策**

```typescript
// ✅ 正确的文件结构
gui/src/
├── main/
│   └── events/
│       └── MainEventBus.ts          # 主进程事件总线
├── shared/
│   └── events/
│       ├── RendererEventBus.ts      # 渲染进程事件总线（跨环境兼容）
│       └── EventBusExample.ts       # 使用示例和工具
└── renderer/
    └── composables/
        └── useBuildEvents.ts        # Vue Composable
```

## 📝 文件放置规则

### **Rule 1: 环境特定性原则**

```typescript
// ✅ 主进程专用 - 放在 main/
// gui/src/main/events/MainEventBus.ts
import { EventEmitter } from 'events';
import { BrowserWindow } from 'electron';

class MainEventBus extends EventEmitter {
  publishBuildProgress(data: BuildProgressEvent) {
    // 可以安全使用主进程 API
    const windows = BrowserWindow.getAllWindows();
    // ...
  }
}

// ✅ 渲染进程专用 - 放在 renderer/
// gui/src/renderer/composables/useBuildEvents.ts
import { onUnmounted } from 'vue';

export function useBuildEvents() {
  // Vue 特定的逻辑
  onUnmounted(() => {
    // 清理逻辑
  });
}
```

### **Rule 2: 跨环境兼容性原则**

```typescript
// ✅ 跨环境兼容 - 放在 shared/
// gui/src/shared/events/RendererEventBus.ts
const isRendererProcess = typeof globalThis !== 'undefined' && 
  typeof (globalThis as any).window !== 'undefined';

class RendererEventBus {
  constructor() {
    if (isRendererProcess) {
      this.setupEventListeners();
    } else {
      console.log('Running in main process, skipping initialization');
    }
  }
}
```

### **Rule 3: 类型定义共享原则**

```typescript
// ✅ 类型定义 - 放在 shared/types/
// gui/src/shared/types/events.ts
export interface BuildProgressEvent {
  buildId: string;
  platform: string;
  progress: number;
  message: string;
  timestamp: string;
}

// 主进程中使用
import type { BuildProgressEvent } from '../../shared/types/events';

// 渲染进程中使用
import type { BuildProgressEvent } from '@shared/types/events';
```

### **Rule 4: 常量定义统一原则**

```typescript
// ✅ 常量定义 - 放在 shared/constants/
// gui/src/shared/constants/ipc-commands.ts
export const IPC_COMMANDS = {
  EVENTS: {
    BUILD_PROGRESS: 'build-progress',
    BUILD_COMPLETE: 'build-complete',
    VERSION_CHANGED: 'version-changed'
  }
};

// 避免重复定义，统一引用
import { IPC_COMMANDS } from '@shared/constants/ipc-commands';
```

## 🔧 编译配置对应关系

### **TypeScript 配置文件**

```json
// tsconfig.main.json - 主进程编译配置
{
  "include": [
    "src/main/**/*",
    "src/shared/**/*"    // 包含共享代码
  ],
  "exclude": [
    "src/renderer/**/*"  // 排除渲染进程代码
  ]
}

// tsconfig.renderer.json - 渲染进程编译配置
{
  "include": [
    "src/renderer/**/*",
    "src/shared/**/*"    // 包含共享代码
  ],
  "exclude": [
    "src/main/**/*"      // 排除主进程代码
  ]
}
```

### **编译验证**

```bash
# 验证主进程编译
npm run build:main

# 验证渲染进程编译
npm run build:renderer

# 验证 preload 编译
npm run build:preload
```

## 📊 导入路径规范

### **路径别名配置**

```typescript
// vite.config.ts / tsconfig.json
{
  "paths": {
    "@/*": ["src/*"],
    "@main/*": ["src/main/*"],
    "@renderer/*": ["src/renderer/*"],
    "@shared/*": ["src/shared/*"]
  }
}
```

### **导入示例**

```typescript
// ✅ 主进程中的导入
import { mainEventBus } from '../events/MainEventBus';
import { BuildService } from '../services/build/BuildService';
import { IPC_COMMANDS } from '@shared/constants/ipc-commands';

// ✅ 渲染进程中的导入
import { rendererEventBus } from '@shared/events/RendererEventBus';
import { apiClient } from '@shared/api/VersionCraftAPIClient';
import type { BuildProgressEvent } from '@shared/types/events';

// ✅ 共享代码中的导入
import { ErrorHandler } from '../services/ErrorHandler';
import { IPC_COMMANDS } from '../constants/ipc-commands';
```

## 🚫 反面案例分析

### **错误案例1: 环境混淆**

```typescript
// ❌ 错误 - 在 shared/ 中直接使用主进程 API
// gui/src/shared/events/MainEventBus.ts
import { BrowserWindow } from 'electron'; // 渲染进程编译时会报错

class MainEventBus {
  notify() {
    const windows = BrowserWindow.getAllWindows(); // 编译错误
  }
}
```

**问题分析：**
- 违反了跨环境兼容性原则
- 导致渲染进程编译失败
- 破坏了代码的可移植性

### **错误案例2: 循环依赖**

```typescript
// ❌ 错误 - 循环依赖
// gui/src/main/services/VersionCraftService.ts
import { mainEventBus } from '../events/MainEventBus';

// gui/src/main/events/MainEventBus.ts
import { VersionCraftService } from '../services/VersionCraftService'; // 循环依赖
```

**解决方案：**
```typescript
// ✅ 正确 - 使用依赖注入
// gui/src/main/events/MainEventBus.ts
class MainEventBus {
  private versionCraftService?: VersionCraftService;
  
  setVersionCraftService(service: VersionCraftService) {
    this.versionCraftService = service;
  }
}
```

### **错误案例3: 类型定义分散**

```typescript
// ❌ 错误 - 类型定义分散在各个文件中
// gui/src/main/events/MainEventBus.ts
interface BuildProgressEvent { /* ... */ }

// gui/src/renderer/stores/build.ts
interface BuildProgressEvent { /* ... */ } // 重复定义

// ✅ 正确 - 统一的类型定义
// gui/src/shared/types/events.ts
export interface BuildProgressEvent { /* ... */ }
```

## 🎯 最佳实践清单

### **文件放置检查清单**
- [ ] 是否只在特定环境中使用？→ 放在对应的 `main/` 或 `renderer/` 目录
- [ ] 是否需要跨环境共享？→ 放在 `shared/` 目录
- [ ] 是否包含环境特定的 API 调用？→ 不应放在 `shared/` 目录
- [ ] 是否是类型定义？→ 放在 `shared/types/` 目录
- [ ] 是否是常量定义？→ 放在 `shared/constants/` 目录

### **代码组织检查清单**
- [ ] 导入路径是否使用了正确的别名？
- [ ] 是否存在循环依赖？
- [ ] 类型定义是否统一管理？
- [ ] 常量是否避免重复定义？
- [ ] 编译配置是否正确包含/排除了相应目录？

### **命名规范检查清单**
- [ ] 文件名是否使用 PascalCase（类文件）或 camelCase（工具文件）？
- [ ] 目录名是否使用 kebab-case 或 camelCase？
- [ ] 导出的类/接口是否使用 PascalCase？
- [ ] 变量/函数是否使用 camelCase？

## 🔍 代码审查要点

### **架构层面**
1. **目录结构** - 是否符合环境隔离原则？
2. **依赖关系** - 是否存在不合理的跨目录依赖？
3. **编译配置** - 是否正确配置了包含/排除规则？

### **代码层面**
1. **导入路径** - 是否使用了正确的路径别名？
2. **类型安全** - 是否正确使用了共享的类型定义？
3. **环境检测** - 跨环境代码是否包含了环境检测逻辑？

### **维护层面**
1. **文档更新** - 新增文件是否更新了相关文档？
2. **测试覆盖** - 是否为新的组织结构编写了测试？
3. **迁移指南** - 结构变更是否提供了迁移指南？

## 📚 相关文档

- [事件总线 vs IPC调用指南](./EventBus-vs-IPC-Guide.md)
- [迁移指南](./EventBus-Migration-Guide.md)
- [最佳实践](./EventBus-Best-Practices.md)
- [快速参考卡](./Quick-Reference-Card.md)

---

📝 **文档版本**: v1.0  
🕒 **最后更新**: 2024-01-XX  
👥 **维护者**: Version-Craft Team  
💡 **核心原则**: 环境隔离、功能内聚、依赖清晰、编译优化

## 🛠️ 实用工具和脚本

### **目录结构验证脚本**

```bash
#!/bin/bash
# scripts/validate-structure.sh

echo "🔍 验证项目结构..."

# 检查必需的目录
required_dirs=(
  "src/main/events"
  "src/main/services"
  "src/main/ipc"
  "src/renderer/components"
  "src/renderer/stores"
  "src/shared/types"
  "src/shared/constants"
  "src/shared/events"
)

for dir in "${required_dirs[@]}"; do
  if [ ! -d "gui/$dir" ]; then
    echo "❌ 缺少目录: $dir"
    exit 1
  else
    echo "✅ 目录存在: $dir"
  fi
done

echo "🎉 项目结构验证通过！"
```

### **导入路径检查脚本**

```javascript
// scripts/check-imports.js
const fs = require('fs');
const path = require('path');
const glob = require('glob');

const checkImports = () => {
  const files = glob.sync('gui/src/**/*.ts', { ignore: 'gui/src/**/*.d.ts' });
  const issues = [];

  files.forEach(file => {
    const content = fs.readFileSync(file, 'utf8');
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      // 检查是否使用了相对路径而不是别名
      if (line.includes('import') && line.includes('../../../')) {
        issues.push({
          file,
          line: index + 1,
          issue: '使用了过深的相对路径，建议使用路径别名',
          content: line.trim()
        });
      }

      // 检查是否在 shared 中导入了主进程特定的模块
      if (file.includes('/shared/') && line.includes('electron')) {
        issues.push({
          file,
          line: index + 1,
          issue: 'shared 代码不应直接导入 electron 模块',
          content: line.trim()
        });
      }
    });
  });

  if (issues.length > 0) {
    console.log('❌ 发现导入问题:');
    issues.forEach(issue => {
      console.log(`\n文件: ${issue.file}:${issue.line}`);
      console.log(`问题: ${issue.issue}`);
      console.log(`代码: ${issue.content}`);
    });
    process.exit(1);
  } else {
    console.log('✅ 导入路径检查通过！');
  }
};

checkImports();
```

### **代码生成模板**

```typescript
// scripts/templates/event-bus-template.ts
/**
 * 事件总线模板生成器
 * 使用方法: node scripts/generate-event-bus.js <EventName>
 */

export const generateMainEventBus = (eventName: string) => `
/**
 * ${eventName} 主进程事件总线
 * 自动生成于 ${new Date().toISOString()}
 */

import { EventEmitter } from 'events';
import { mainEventBus } from '../MainEventBus';

export interface ${eventName}ProgressEvent {
  id: string;
  progress: number;
  message: string;
  timestamp: string;
}

export interface ${eventName}CompleteEvent {
  id: string;
  success: boolean;
  result?: any;
  error?: string;
  timestamp: string;
}

class ${eventName}EventBus extends EventEmitter {
  publish${eventName}Progress(data: ${eventName}ProgressEvent): void {
    console.log(\`📡 [${eventName}EventBus] Publishing progress:\`, data);
    mainEventBus.emit('${eventName.toLowerCase()}-progress', data);
  }

  publish${eventName}Complete(data: ${eventName}CompleteEvent): void {
    console.log(\`✅ [${eventName}EventBus] Publishing complete:\`, data);
    mainEventBus.emit('${eventName.toLowerCase()}-complete', data);
  }
}

export const ${eventName.toLowerCase()}EventBus = new ${eventName}EventBus();
`;

export const generateRendererEventBus = (eventName: string) => `
/**
 * ${eventName} 渲染进程事件监听器
 * 自动生成于 ${new Date().toISOString()}
 */

import { rendererEventBus } from '@shared/events/RendererEventBus';
import type { ${eventName}ProgressEvent, ${eventName}CompleteEvent } from '../../main/events/${eventName}EventBus';

export function use${eventName}Events(id?: string) {
  const subscribe${eventName}Progress = (callback: (data: ${eventName}ProgressEvent) => void) => {
    return rendererEventBus.subscribe('${eventName.toLowerCase()}-progress', (data: ${eventName}ProgressEvent) => {
      if (!id || data.id === id) {
        callback(data);
      }
    });
  };

  const subscribe${eventName}Complete = (callback: (data: ${eventName}CompleteEvent) => void) => {
    return rendererEventBus.subscribe('${eventName.toLowerCase()}-complete', (data: ${eventName}CompleteEvent) => {
      if (!id || data.id === id) {
        callback(data);
      }
    });
  };

  return {
    subscribe${eventName}Progress,
    subscribe${eventName}Complete
  };
}
`;
```

## 📋 项目结构检查清单

### **新功能开发检查清单**

```markdown
## 新功能开发检查清单

### 📁 文件放置
- [ ] 主进程代码放在 `src/main/` 目录
- [ ] 渲染进程代码放在 `src/renderer/` 目录
- [ ] 共享代码放在 `src/shared/` 目录
- [ ] 类型定义放在 `src/shared/types/` 目录

### 🔗 导入路径
- [ ] 使用了正确的路径别名 (`@main/`, `@renderer/`, `@shared/`)
- [ ] 避免了过深的相对路径 (`../../../`)
- [ ] 共享代码没有导入环境特定的模块

### 🎯 事件系统
- [ ] 主进程事件总线放在 `src/main/events/`
- [ ] 渲染进程事件监听器使用环境检测
- [ ] 事件类型定义统一管理

### 📝 文档更新
- [ ] 更新了相关的 API 文档
- [ ] 添加了使用示例
- [ ] 更新了架构图（如有必要）

### 🧪 测试覆盖
- [ ] 编写了单元测试
- [ ] 验证了跨环境兼容性
- [ ] 测试了错误处理逻辑
```

### **代码审查检查清单**

```markdown
## 代码审查检查清单

### 🏗️ 架构设计
- [ ] 符合环境隔离原则
- [ ] 没有循环依赖
- [ ] 依赖关系清晰合理

### 💻 代码质量
- [ ] 使用了 TypeScript 类型注解
- [ ] 包含了适当的错误处理
- [ ] 遵循了命名规范

### 🔧 配置文件
- [ ] 更新了 tsconfig 配置（如有必要）
- [ ] 更新了路径别名配置
- [ ] 更新了构建脚本

### 📚 文档维护
- [ ] 代码包含了必要的注释
- [ ] 更新了相关文档
- [ ] 提供了使用示例
```

## 🔄 重构指南

### **目录结构重构步骤**

1. **评估现状**
   ```bash
   # 分析当前结构
   find gui/src -name "*.ts" -type f | head -20

   # 检查导入关系
   grep -r "import.*from" gui/src --include="*.ts" | head -10
   ```

2. **制定迁移计划**
   ```markdown
   ## 迁移计划

   ### 阶段1: 创建新目录结构
   - 创建标准目录
   - 设置路径别名

   ### 阶段2: 移动文件
   - 按环境分离文件
   - 更新导入路径

   ### 阶段3: 验证和测试
   - 运行编译测试
   - 执行功能测试
   ```

3. **执行迁移**
   ```bash
   # 创建目录结构
   mkdir -p gui/src/{main,renderer,shared}/{events,services,types}

   # 移动文件（示例）
   mv gui/src/events/MainEventBus.ts gui/src/main/events/
   mv gui/src/events/RendererEventBus.ts gui/src/shared/events/

   # 更新导入路径
   find gui/src -name "*.ts" -exec sed -i 's|../events/MainEventBus|../main/events/MainEventBus|g' {} \;
   ```

4. **验证结果**
   ```bash
   # 编译测试
   npm run build

   # 运行测试
   npm test

   # 启动应用
   npm start
   ```

## 🎯 常见问题解答

### **Q: 为什么不把所有事件相关代码都放在 shared/events？**
A: 因为 `MainEventBus` 依赖主进程特定的 API（如 `EventEmitter`, `BrowserWindow`），放在 shared 目录会导致渲染进程编译失败。

### **Q: 如何处理跨环境的类型定义？**
A: 将类型定义放在 `shared/types` 目录，然后在不同环境中导入使用：
```typescript
// shared/types/events.ts
export interface BuildProgressEvent { ... }

// main/events/MainEventBus.ts
import type { BuildProgressEvent } from '../../shared/types/events';

// renderer/stores/build.ts
import type { BuildProgressEvent } from '@shared/types/events';
```

### **Q: 什么时候需要使用环境检测？**
A: 当代码需要在多个环境中运行，但包含环境特定逻辑时：
```typescript
// shared/events/RendererEventBus.ts
const isRendererProcess = typeof window !== 'undefined';

if (isRendererProcess) {
  // 渲染进程特定逻辑
} else {
  // 主进程或其他环境的处理
}
```

### **Q: 如何避免循环依赖？**
A: 使用依赖注入、事件系统或接口抽象：
```typescript
// 避免直接依赖，使用事件通信
mainEventBus.emit('service-ready', serviceInstance);

// 或使用依赖注入
class MainEventBus {
  setService(service: SomeService) {
    this.service = service;
  }
}
```

## 🚀 未来规划

### **短期目标**
- [ ] 完善自动化检查脚本
- [ ] 建立 CI/CD 中的结构验证
- [ ] 创建更多代码生成模板

### **中期目标**
- [ ] 开发 VSCode 插件辅助开发
- [ ] 建立架构决策记录（ADR）
- [ ] 完善性能监控和分析

### **长期目标**
- [ ] 探索微前端架构
- [ ] 考虑模块联邦方案
- [ ] 建立跨项目的代码组织标准

---

🔧 **工具支持**: 提供了实用的脚本和模板来帮助维护正确的代码组织结构
📊 **持续改进**: 基于实际使用经验不断优化组织规范
🤝 **团队协作**: 通过清晰的规范提高团队开发效率
