# 🎯 事件总线最佳实践指南

## ❌ **常见错误和废物代码**

### **错误1：在 Pinia Store 中使用 onUnmounted**

```typescript
// ❌ 错误 - 这段代码永远不会执行
import { defineStore } from 'pinia';
import { onUnmounted } from 'vue';

export const useBuildStore = defineStore('build', () => {
  // ... store 逻辑

  // ❌ 这是废物代码！onUnmounted 在 store 中不会被调用
  onUnmounted(() => {
    cleanupEventListeners(); // 永远不会执行
  });

  return { /* ... */ };
});
```

**问题分析：**
- `onUnmounted` 只在 Vue 组件的 `setup()` 函数中有效
- Pinia store 不是 Vue 组件，没有生命周期
- 这会导致事件监听器无法清理，造成内存泄漏

### **错误2：忘记清理事件监听器**

```typescript
// ❌ 错误 - 内存泄漏
const setupEvents = () => {
  rendererEventBus.subscribeBuildProgress((data) => {
    // 处理事件
  });
  // 忘记保存 unsubscriber，无法清理
};
```

### **错误3：重复设置监听器**

```typescript
// ❌ 错误 - 重复监听
const startBuild = () => {
  // 每次调用都会添加新的监听器
  rendererEventBus.subscribeBuildProgress(handleProgress);
  // 导致同一个事件被处理多次
};
```

## ✅ **正确的实践方式**

### **方式1：在 Vue 组件中使用 Composable**

```vue
<template>
  <div>
    <div v-if="isBuilding">构建进度: {{ buildProgress?.progress }}%</div>
    <div v-if="buildComplete">构建{{ buildComplete.success ? '成功' : '失败' }}</div>
  </div>
</template>

<script setup>
import { useBuildEventsByID } from '@/composables/useBuildEvents';

const props = defineProps<{ buildId: string }>();

// ✅ 正确 - 使用 composable 管理事件监听器
const {
  buildProgress,
  buildComplete,
  isBuilding,
  startListening,
  stopListening
} = useBuildEventsByID(props.buildId);

// 开始监听
startListening();

// onUnmounted 会在 composable 内部自动调用
</script>
```

### **方式2：手动管理生命周期**

```vue
<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { rendererEventBus } from '@shared/events/RendererEventBus';

const unsubscribers = ref<Array<() => void>>([]);

onMounted(() => {
  // ✅ 正确 - 保存 unsubscriber
  const progressUnsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
    console.log('构建进度:', data.progress);
  });

  const completeUnsubscriber = rendererEventBus.subscribeBuildComplete((data) => {
    console.log('构建完成:', data.success);
  });

  // ✅ 正确 - 存储所有 unsubscriber
  unsubscribers.value.push(progressUnsubscriber, completeUnsubscriber);
});

onUnmounted(() => {
  // ✅ 正确 - 清理所有监听器
  unsubscribers.value.forEach(unsubscriber => unsubscriber());
  unsubscribers.value = [];
});
</script>
```

### **方式3：在 Pinia Store 中正确管理**

```typescript
// ✅ 正确 - Store 只提供管理方法，不直接管理生命周期
export const useBuildStore = defineStore('build', () => {
  const eventUnsubscribers = ref<Array<() => void>>([]);

  // ✅ 正确 - 提供设置方法，返回清理函数
  const setupBuildEventListeners = (buildId: string) => {
    // 清理之前的监听器
    cleanupEventListeners();

    const progressUnsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
      if (data.buildId === buildId) {
        // 更新 store 状态
      }
    });

    eventUnsubscribers.value.push(progressUnsubscriber);

    // ✅ 返回清理函数，供组件使用
    return () => {
      progressUnsubscriber();
    };
  };

  const cleanupEventListeners = () => {
    eventUnsubscribers.value.forEach(unsubscriber => unsubscriber());
    eventUnsubscribers.value = [];
  };

  return {
    setupBuildEventListeners,
    cleanupEventListeners // ✅ 暴露清理方法供组件调用
  };
});
```

然后在组件中使用：

```vue
<script setup>
import { onUnmounted } from 'vue';
import { useBuildStore } from '@/stores/build';

const buildStore = useBuildStore();

// ✅ 正确 - 在组件中管理生命周期
const cleanup = buildStore.setupBuildEventListeners('build-123');

onUnmounted(() => {
  cleanup(); // ✅ 组件卸载时清理
});
</script>
```

## 🔍 **检测废物代码的方法**

### **1. 搜索模式**
```bash
# 搜索可能的废物代码
grep -r "onUnmounted" src/stores/
grep -r "onMounted" src/stores/
```

### **2. 代码审查清单**
- [ ] `onUnmounted` 是否只在 Vue 组件中使用？
- [ ] 每个 `subscribe` 调用是否都保存了 `unsubscriber`？
- [ ] 每个 `unsubscriber` 是否都在适当的时候被调用？
- [ ] 是否有重复的事件监听器设置？

### **3. 运行时检测**
```typescript
// 在开发环境中检测内存泄漏
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    const stats = rendererEventBus.getSubscriptionStats();
    console.log('事件订阅统计:', Object.fromEntries(stats));
    
    // 如果订阅数量持续增长，可能有内存泄漏
    const totalSubscriptions = Array.from(stats.values()).reduce((a, b) => a + b, 0);
    if (totalSubscriptions > 50) {
      console.warn('⚠️ 事件订阅数量过多，可能存在内存泄漏:', totalSubscriptions);
    }
  }, 10000);
}
```

## 🎯 **性能优化建议**

### **1. 避免频繁的订阅/取消订阅**
```typescript
// ❌ 错误 - 频繁操作
const handleSomething = () => {
  const unsubscriber = rendererEventBus.subscribeBuildProgress(handler);
  // 立即取消订阅
  unsubscriber();
};

// ✅ 正确 - 长期订阅
const unsubscriber = rendererEventBus.subscribeBuildProgress(handler);
// 在组件卸载时才取消订阅
```

### **2. 使用事件过滤**
```typescript
// ✅ 正确 - 在监听器中过滤
const unsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
  if (data.buildId !== targetBuildId) return; // 早期返回
  // 处理相关事件
});
```

### **3. 批量处理事件**
```typescript
// ✅ 正确 - 防抖处理
import { debounce } from 'lodash-es';

const debouncedHandler = debounce((data) => {
  // 批量处理事件
}, 100);

const unsubscriber = rendererEventBus.subscribeBuildProgress(debouncedHandler);
```

## 📊 **内存泄漏检测工具**

```typescript
// 开发工具：事件监听器泄漏检测
export class EventLeakDetector {
  private subscriptionHistory = new Map<string, number>();
  
  checkForLeaks() {
    const currentStats = rendererEventBus.getSubscriptionStats();
    
    currentStats.forEach((count, eventName) => {
      const previousCount = this.subscriptionHistory.get(eventName) || 0;
      
      if (count > previousCount + 10) {
        console.warn(`⚠️ 可能的内存泄漏: ${eventName} 订阅数从 ${previousCount} 增加到 ${count}`);
      }
      
      this.subscriptionHistory.set(eventName, count);
    });
  }
}

// 在开发环境中使用
if (process.env.NODE_ENV === 'development') {
  const detector = new EventLeakDetector();
  setInterval(() => detector.checkForLeaks(), 5000);
}
```

通过遵循这些最佳实践，可以避免废物代码，确保事件监听器得到正确管理，防止内存泄漏。
