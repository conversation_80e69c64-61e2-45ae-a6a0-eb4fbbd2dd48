# 🚀 事件总线迁移指南

## 📋 概述

本指南帮助开发者从旧的IPC事件系统迁移到新的事件总线系统。新系统提供了更好的类型安全、调试能力和性能。

## 🔄 迁移对比

### **旧方式 (已弃用)**

```typescript
// ❌ 旧的事件监听方式
window.electronAPI.onBuildProgress((data) => {
  console.log('构建进度:', data.progress);
});

// ❌ 旧的事件发送方式 (主进程)
window.webContents.send(IPC_COMMANDS.EVENTS.BUILD_PROGRESS, data);
```

### **新方式 (推荐)**

```typescript
// ✅ 新的事件监听方式
import { rendererEventBus } from '@/events/RendererEventBus';

const unsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
  console.log('构建进度:', data.progress);
});

// ✅ 新的事件发送方式 (主进程)
import { mainEventBus } from '@/events/MainEventBus';

mainEventBus.publishBuildProgress({
  buildId: 'build-123',
  platform: 'web-mobile',
  progress: 50,
  message: '正在编译...',
  elapsed: 30000,
  timestamp: new Date().toISOString()
});
```

## 📚 API 迁移表

| 旧API | 新API | 说明 |
|-------|-------|------|
| `window.electronAPI.onBuildProgress()` | `rendererEventBus.subscribeBuildProgress()` | 监听构建进度 |
| `window.electronAPI.onVersionChanged()` | `rendererEventBus.subscribeVersionChanged()` | 监听版本变更 |
| `window.electronAPI.offBuildProgress()` | `unsubscriber()` | 取消监听 |
| `apiClient.events.onBuildProgress()` | `rendererEventBus.subscribeBuildProgress()` | API客户端事件监听 |
| `webContents.send()` | `mainEventBus.publishXXX()` | 主进程事件发送 |

## 🎯 具体迁移步骤

### **1. Vue 组件迁移**

**旧代码:**
```vue
<script setup>
import { onMounted, onUnmounted } from 'vue';

onMounted(() => {
  if (window.electronAPI) {
    window.electronAPI.onBuildProgress((data) => {
      // 处理构建进度
    });
  }
});

onUnmounted(() => {
  if (window.electronAPI) {
    window.electronAPI.offBuildProgress();
  }
});
</script>
```

**新代码:**
```vue
<script setup>
import { onMounted, onUnmounted } from 'vue';
import { rendererEventBus } from '@/events/RendererEventBus';

const unsubscribers = [];

onMounted(() => {
  const buildProgressUnsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
    // 处理构建进度
  });
  
  unsubscribers.push(buildProgressUnsubscriber);
});

onUnmounted(() => {
  unsubscribers.forEach(unsubscriber => unsubscriber());
});
</script>
```

### **2. Pinia Store 迁移**

**旧代码:**
```typescript
// stores/build.ts
const setupEventListeners = () => {
  if (window.electronAPI) {
    window.electronAPI.on(IPC_COMMANDS.EVENTS.BUILD_PROGRESS, (data) => {
      // 更新状态
    });
  }
};
```

**新代码:**
```typescript
// stores/build.ts
import { rendererEventBus } from '@/events/RendererEventBus';

const eventUnsubscribers = ref<Array<() => void>>([]);

const setupEventListeners = () => {
  const progressUnsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
    // 更新状态
  });
  
  eventUnsubscribers.value.push(progressUnsubscriber);
};

const cleanup = () => {
  eventUnsubscribers.value.forEach(unsubscriber => unsubscriber());
  eventUnsubscribers.value = [];
};
```

### **3. 主进程服务迁移**

**旧代码:**
```typescript
// 主进程服务
notifyBuildProgress(buildId: string, data: any): void {
  const windows = BrowserWindow.getAllWindows();
  windows.forEach(window => {
    window.webContents.send(IPC_COMMANDS.EVENTS.BUILD_PROGRESS, data);
  });
}
```

**新代码:**
```typescript
// 主进程服务
import { mainEventBus } from '@/events/MainEventBus';

notifyBuildProgress(buildId: string, data: any): void {
  mainEventBus.publishBuildProgress({
    buildId,
    platform: data.platform || '',
    progress: data.progress || 0,
    message: data.message || '',
    elapsed: data.elapsed || 0,
    timestamp: new Date().toISOString()
  });
}
```

## ⚠️ 注意事项

### **1. 类型安全**
新的事件总线提供完整的TypeScript类型支持：

```typescript
import type { BuildProgressEvent } from '@/events/MainEventBus';

const handleBuildProgress = (data: BuildProgressEvent) => {
  // data 现在有完整的类型提示
  console.log(data.buildId, data.progress, data.platform);
};
```

### **2. 内存管理**
必须正确清理事件监听器：

```typescript
// ✅ 正确的清理方式
const unsubscriber = rendererEventBus.subscribeBuildProgress(handler);

onUnmounted(() => {
  unsubscriber(); // 必须调用
});

// ❌ 错误 - 会导致内存泄漏
rendererEventBus.subscribeBuildProgress(handler);
// 忘记清理
```

### **3. 调试支持**
新系统提供更好的调试功能：

```typescript
// 获取事件统计
const stats = rendererEventBus.getEventStats();
console.log('事件统计:', stats);

// 获取订阅统计
const subscriptions = rendererEventBus.getSubscriptionStats();
console.log('订阅统计:', subscriptions);
```

## 🔧 兼容性说明

### **向后兼容**
- 旧的IPC事件系统仍然可用，但会显示弃用警告
- 建议逐步迁移，不需要一次性全部更改
- preload.ts 中的桥接函数保留，但标记为已弃用

### **弃用警告**
使用旧API时会在控制台看到警告：
```
⚠️ [Preload] onBuildProgress is deprecated. Please use rendererEventBus.subscribeBuildProgress()
```

## 🎉 迁移收益

### **性能提升**
- 减少了事件传递的中间层
- 更高效的事件分发机制
- 防重复事件发送

### **开发体验**
- 完整的TypeScript类型支持
- 更好的调试和日志功能
- 统一的事件管理接口

### **维护性**
- 更清晰的事件流向
- 更容易定位问题
- 更好的代码组织

## 📞 获取帮助

如果在迁移过程中遇到问题：

1. 查看 `EventBusExample.ts` 中的使用示例
2. 检查控制台的调试日志
3. 使用事件统计功能排查问题

```typescript
import { getEventBusStats } from '@/shared/events/EventBusExample';

// 查看详细统计
console.log('事件总线统计:', getEventBusStats());
```
