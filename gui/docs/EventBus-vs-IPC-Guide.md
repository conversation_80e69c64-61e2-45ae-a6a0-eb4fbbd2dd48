# 🎯 事件总线 vs IPC调用 - 完整使用指南

## 📋 概述

本文档详细说明了事件总线（EventBus）和IPC调用（electronAPI.invoke）的使用场景、区别和最佳实践。两者在Version-Craft GUI中承担不同的职责，正确使用能显著提升应用性能和用户体验。

## 🔄 核心区别对比

### **事件总线 (EventBus)**
- **方向**: 主进程 → 渲染进程（单向）
- **模式**: 发布订阅模式
- **特点**: 异步、实时、一对多广播
- **用途**: 状态通知、进度更新、事件广播

### **IPC调用 (electronAPI.invoke)**
- **方向**: 渲染进程 ↔ 主进程（双向）
- **模式**: 请求响应模式
- **特点**: 同步/异步、一对一通信
- **用途**: 数据查询、操作执行、文件处理

## 📊 使用场景对比表

| 场景类型 | 具体场景 | 事件总线 | IPC调用 | 推荐方案 | 原因 |
|---------|---------|----------|---------|----------|------|
| **构建管理** | 启动构建 | ❌ | ✅ | IPC调用 | 需要返回buildId |
| | 构建进度更新 | ✅ | ❌ | 事件总线 | 实时推送进度 |
| | 取消构建 | ❌ | ✅ | IPC调用 | 需要确认操作结果 |
| | 构建完成通知 | ✅ | ❌ | 事件总线 | 状态变更通知 |
| | 获取构建历史 | ❌ | ✅ | IPC调用 | 查询历史数据 |
| **版本管理** | 版本升级 | ❌ | ✅ | IPC调用 | 需要返回新版本号 |
| | 版本变更通知 | ✅ | ❌ | 事件总线 | 通知UI更新 |
| | 获取当前版本 | ❌ | ✅ | IPC调用 | 查询版本信息 |
| **项目管理** | 选择项目文件夹 | ❌ | ✅ | IPC调用 | 文件对话框操作 |
| | 项目初始化完成 | ✅ | ❌ | 事件总线 | 状态变更通知 |
| | 获取项目信息 | ❌ | ✅ | IPC调用 | 查询项目数据 |
| **部署管理** | 开始部署 | ❌ | ✅ | IPC调用 | 需要返回部署ID |
| | 部署进度更新 | ✅ | ❌ | 事件总线 | 实时推送状态 |
| | 部署完成通知 | ✅ | ❌ | 事件总线 | 结果通知 |
| **配置管理** | 保存配置 | ❌ | ✅ | IPC调用 | 需要确认保存结果 |
| | 配置变更通知 | ✅ | ❌ | 事件总线 | 通知其他组件 |
| | 导入/导出配置 | ❌ | ✅ | IPC调用 | 文件操作 |

## 🎨 架构设计模式

### **模式1: 操作-监听模式**
```typescript
// 1. 使用IPC启动操作
const buildResult = await apiClient.build.start(options);

// 2. 使用事件总线监听进度
const unsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
  if (data.buildId === buildResult.buildId) {
    updateProgress(data);
  }
});

// 3. 监听完成事件
rendererEventBus.subscribeBuildComplete((data) => {
  if (data.buildId === buildResult.buildId) {
    handleComplete(data);
    unsubscriber(); // 清理监听器
  }
});
```

### **模式2: 查询-缓存模式**
```typescript
// 使用IPC查询数据并缓存
const projectInfo = await apiClient.project.getInfo();
const buildHistory = await apiClient.build.getHistory();

// 使用事件总线监听变更，更新缓存
rendererEventBus.subscribeVersionChanged(() => {
  // 重新查询版本信息
  refreshVersionInfo();
});
```

### **模式3: 混合响应模式**
```typescript
class BuildManager {
  async startBuild(options: BuildOptions) {
    try {
      // IPC: 启动构建
      const result = await apiClient.build.start(options);
      
      // EventBus: 设置监听
      this.setupProgressListeners(result.buildId);
      
      return result;
    } catch (error) {
      // IPC: 错误处理
      throw error;
    }
  }
  
  private setupProgressListeners(buildId: string) {
    // EventBus: 实时更新
    const progressSub = rendererEventBus.subscribeBuildProgress((data) => {
      if (data.buildId === buildId) {
        this.updateProgress(data);
      }
    });
    
    const completeSub = rendererEventBus.subscribeBuildComplete((data) => {
      if (data.buildId === buildId) {
        this.handleComplete(data);
        progressSub();
        completeSub();
      }
    });
  }
}
```

## 🔧 实际代码示例

### **示例1: 构建流程完整实现**

```vue
<template>
  <div class="build-panel">
    <button @click="startBuild" :disabled="isBuilding">
      {{ isBuilding ? '构建中...' : '开始构建' }}
    </button>
    
    <div v-if="isBuilding" class="progress-bar">
      <div class="progress" :style="{ width: `${buildProgress}%` }"></div>
      <span>{{ buildMessage }}</span>
    </div>
    
    <div v-if="buildResult" class="result">
      构建{{ buildResult.success ? '成功' : '失败' }}
      <span v-if="!buildResult.success">: {{ buildResult.error }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onUnmounted } from 'vue';
import { apiClient } from '@shared/api/VersionCraftAPIClient';
import { rendererEventBus } from '@shared/events/RendererEventBus';

// 响应式状态
const isBuilding = ref(false);
const buildProgress = ref(0);
const buildMessage = ref('');
const buildResult = ref(null);

// 事件监听器清理函数
const unsubscribers = ref([]);

// 启动构建
const startBuild = async () => {
  try {
    isBuilding.value = true;
    buildProgress.value = 0;
    buildMessage.value = '准备构建...';
    buildResult.value = null;
    
    // ✅ IPC调用: 启动构建操作
    const result = await apiClient.build.start({
      platform: 'web-mobile',
      clean: true
    });
    
    console.log('构建启动成功:', result.buildId);
    
    // ✅ 事件总线: 监听构建进度
    const progressUnsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
      if (data.buildId === result.buildId) {
        buildProgress.value = data.progress;
        buildMessage.value = data.message;
      }
    });
    
    // ✅ 事件总线: 监听构建完成
    const completeUnsubscriber = rendererEventBus.subscribeBuildComplete((data) => {
      if (data.buildId === result.buildId) {
        isBuilding.value = false;
        buildResult.value = data;
        
        // 清理监听器
        progressUnsubscriber();
        completeUnsubscriber();
      }
    });
    
    // 保存清理函数
    unsubscribers.value.push(progressUnsubscriber, completeUnsubscriber);
    
  } catch (error) {
    console.error('启动构建失败:', error);
    isBuilding.value = false;
    buildResult.value = { success: false, error: error.message };
  }
};

// 组件卸载时清理
onUnmounted(() => {
  unsubscribers.value.forEach(unsubscriber => unsubscriber());
});
</script>
```

### **示例2: 项目信息管理**

```typescript
// stores/project.ts
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { apiClient } from '@shared/api/VersionCraftAPIClient';
import { rendererEventBus } from '@shared/events/RendererEventBus';

export const useProjectStore = defineStore('project', () => {
  const projectInfo = ref(null);
  const currentVersion = ref('');
  const buildHistory = ref([]);
  
  // ✅ IPC调用: 加载项目数据
  const loadProjectData = async () => {
    try {
      const [info, version, history] = await Promise.all([
        apiClient.project.getInfo(),
        apiClient.version.getCurrent(),
        apiClient.build.getHistory()
      ]);
      
      projectInfo.value = info;
      currentVersion.value = version.version;
      buildHistory.value = history;
      
    } catch (error) {
      console.error('加载项目数据失败:', error);
    }
  };
  
  // ✅ 事件总线: 监听实时更新
  const setupEventListeners = () => {
    // 监听版本变更
    const versionUnsubscriber = rendererEventBus.subscribeVersionChanged((data) => {
      currentVersion.value = data.newVersion;
      // 重新加载项目信息
      loadProjectData();
    });
    
    // 监听构建完成，更新历史
    const buildUnsubscriber = rendererEventBus.subscribeBuildComplete((data) => {
      // 重新加载构建历史
      apiClient.build.getHistory().then(history => {
        buildHistory.value = history;
      });
    });
    
    return () => {
      versionUnsubscriber();
      buildUnsubscriber();
    };
  };
  
  return {
    // 状态
    projectInfo,
    currentVersion,
    buildHistory,
    
    // IPC方法
    loadProjectData,
    selectProject: (path) => apiClient.project.select(path),
    saveConfig: (config) => apiClient.config.save(config),
    
    // 事件管理
    setupEventListeners
  };
});
```

### **示例3: 全局状态管理**

```typescript
// composables/useGlobalStatus.ts
import { ref, onMounted, onUnmounted } from 'vue';
import { rendererEventBus } from '@shared/events/RendererEventBus';

export function useGlobalStatus() {
  const activeBuildIds = ref(new Set());
  const activeDeployIds = ref(new Set());
  const lastVersionChange = ref(null);
  
  const unsubscribers = [];
  
  onMounted(() => {
    // ✅ 事件总线: 全局状态监听
    const buildProgressSub = rendererEventBus.subscribeBuildProgress((data) => {
      if (data.progress < 100) {
        activeBuildIds.value.add(data.buildId);
      }
    });
    
    const buildCompleteSub = rendererEventBus.subscribeBuildComplete((data) => {
      activeBuildIds.value.delete(data.buildId);
    });
    
    const versionChangeSub = rendererEventBus.subscribeVersionChanged((data) => {
      lastVersionChange.value = data;
    });
    
    unsubscribers.push(buildProgressSub, buildCompleteSub, versionChangeSub);
  });
  
  onUnmounted(() => {
    unsubscribers.forEach(unsubscriber => unsubscriber());
  });
  
  return {
    activeBuildIds: readonly(activeBuildIds),
    activeDeployIds: readonly(activeDeployIds),
    lastVersionChange: readonly(lastVersionChange),
    
    // 计算属性
    hasActiveBuilds: computed(() => activeBuildIds.value.size > 0),
    hasActiveDeployments: computed(() => activeDeployIds.value.size > 0)
  };
}
```

## ⚠️ 常见错误和反模式

### **错误1: 用事件总线做请求响应**
```typescript
// ❌ 错误 - 事件总线不适合请求响应
const getBuildHistory = () => {
  mainEventBus.publish('get-build-history');
  // 无法直接获取返回值
};

// ✅ 正确 - 使用IPC调用
const getBuildHistory = async () => {
  return await apiClient.build.getHistory();
};
```

### **错误2: 用IPC调用做实时通知**
```typescript
// ❌ 错误 - 轮询获取进度
const pollBuildProgress = async (buildId) => {
  setInterval(async () => {
    const progress = await apiClient.build.getProgress(buildId);
    updateProgress(progress);
  }, 1000);
};

// ✅ 正确 - 使用事件总线
const monitorBuildProgress = (buildId) => {
  return rendererEventBus.subscribeBuildProgress((data) => {
    if (data.buildId === buildId) {
      updateProgress(data);
    }
  });
};
```

### **错误3: 忘记清理事件监听器**
```typescript
// ❌ 错误 - 内存泄漏
const setupListeners = () => {
  rendererEventBus.subscribeBuildProgress(handler);
  // 忘记保存unsubscriber
};

// ✅ 正确 - 正确管理生命周期
const setupListeners = () => {
  const unsubscriber = rendererEventBus.subscribeBuildProgress(handler);
  
  onUnmounted(() => {
    unsubscriber();
  });
};
```

## 🎯 最佳实践总结

### **选择原则**
1. **需要返回值** → IPC调用
2. **实时状态更新** → 事件总线
3. **文件操作** → IPC调用
4. **进度通知** → 事件总线
5. **错误处理** → IPC调用（同步）+ 事件总线（异步通知）

### **性能优化**
1. **批量IPC调用** - 使用Promise.all并行执行
2. **事件过滤** - 在监听器中早期返回不相关事件
3. **及时清理** - 组件卸载时清理所有监听器
4. **避免重复订阅** - 检查是否已经订阅相同事件

### **调试技巧**
1. **启用事件日志** - 在开发环境中记录所有事件
2. **监控订阅数量** - 定期检查是否有内存泄漏
3. **使用统计功能** - 利用事件总线的统计API
4. **错误边界** - 在事件处理器中添加错误捕获

通过遵循这些指导原则，可以构建出高效、可维护的Electron应用程序。

## 📚 API参考

### **事件总线API**

#### **主进程发布事件**
```typescript
import { mainEventBus } from '@/main/events/MainEventBus';

// 发布构建进度
mainEventBus.publishBuildProgress({
  buildId: string,
  platform: string,
  progress: number,
  message: string,
  elapsed?: number,
  timestamp: string
});

// 发布构建完成
mainEventBus.publishBuildComplete({
  buildId: string,
  platform: string,
  success: boolean,
  result?: BuildResult,
  error?: string,
  timestamp: string
});

// 发布版本变更
mainEventBus.publishVersionChanged({
  oldVersion: string,
  newVersion: string,
  type: 'major' | 'minor' | 'patch' | 'prerelease',
  timestamp: string
});
```

#### **渲染进程订阅事件**
```typescript
import { rendererEventBus } from '@shared/events/RendererEventBus';

// 订阅构建进度
const unsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
  console.log('构建进度:', data.progress);
});

// 订阅构建完成
const unsubscriber = rendererEventBus.subscribeBuildComplete((data) => {
  console.log('构建结果:', data.success);
});

// 订阅版本变更
const unsubscriber = rendererEventBus.subscribeVersionChanged((data) => {
  console.log('版本变更:', data.newVersion);
});

// 清理订阅
onUnmounted(() => {
  unsubscriber();
});
```

### **IPC调用API**

#### **构建相关**
```typescript
import { apiClient } from '@shared/api/VersionCraftAPIClient';

// 启动构建
const result = await apiClient.build.start({
  platform: 'web-mobile',
  clean: true,
  optimization: {
    minify: true,
    compress: true
  }
});

// 取消构建
await apiClient.build.cancel(buildId);

// 获取构建历史
const history = await apiClient.build.getHistory();

// 获取构建统计
const stats = await apiClient.build.getStats();
```

#### **版本相关**
```typescript
// 获取当前版本
const version = await apiClient.version.getCurrent();

// 版本升级
const result = await apiClient.version.bump({
  type: 'minor',
  message: '新功能发布',
  createTag: true
});

// 获取版本历史
const history = await apiClient.version.getHistory();
```

#### **项目相关**
```typescript
// 选择项目
const projectPath = await apiClient.project.select();

// 获取项目信息
const info = await apiClient.project.getInfo();

// 项目健康检查
const health = await apiClient.project.healthCheck();
```

## 🔍 调试和监控

### **事件总线调试**
```typescript
// 获取事件统计
const stats = rendererEventBus.getEventStats();
console.log('事件统计:', Object.fromEntries(stats));

// 获取订阅统计
const subscriptions = rendererEventBus.getSubscriptionStats();
console.log('订阅统计:', Object.fromEntries(subscriptions));

// 开发环境监控
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    const totalSubs = Array.from(subscriptions.values()).reduce((a, b) => a + b, 0);
    if (totalSubs > 50) {
      console.warn('⚠️ 订阅数量过多，可能存在内存泄漏:', totalSubs);
    }
  }, 10000);
}
```

### **IPC调用调试**
```typescript
// 启用IPC调试日志
if (process.env.NODE_ENV === 'development') {
  const originalInvoke = window.electronAPI.invoke;
  window.electronAPI.invoke = async (channel, ...args) => {
    console.log(`📡 IPC调用: ${channel}`, args);
    const start = Date.now();

    try {
      const result = await originalInvoke(channel, ...args);
      console.log(`✅ IPC响应: ${channel} (${Date.now() - start}ms)`, result);
      return result;
    } catch (error) {
      console.error(`❌ IPC错误: ${channel} (${Date.now() - start}ms)`, error);
      throw error;
    }
  };
}
```

## 🚀 性能优化指南

### **事件总线优化**
```typescript
// 1. 事件过滤优化
const unsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
  // 早期返回，避免不必要的处理
  if (data.buildId !== targetBuildId) return;

  // 处理相关事件
  updateProgress(data);
});

// 2. 防抖处理
import { debounce } from 'lodash-es';

const debouncedHandler = debounce((data) => {
  updateUI(data);
}, 100);

const unsubscriber = rendererEventBus.subscribeBuildProgress(debouncedHandler);

// 3. 批量更新
const batchUpdates = [];
const flushUpdates = debounce(() => {
  processBatchUpdates(batchUpdates);
  batchUpdates.length = 0;
}, 50);

const unsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
  batchUpdates.push(data);
  flushUpdates();
});
```

### **IPC调用优化**
```typescript
// 1. 并行调用
const loadAllData = async () => {
  const [projectInfo, buildHistory, versionInfo] = await Promise.all([
    apiClient.project.getInfo(),
    apiClient.build.getHistory(),
    apiClient.version.getCurrent()
  ]);

  return { projectInfo, buildHistory, versionInfo };
};

// 2. 缓存机制
class APICache {
  private cache = new Map();
  private ttl = 5 * 60 * 1000; // 5分钟

  async get(key, fetcher) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.data;
    }

    const data = await fetcher();
    this.cache.set(key, { data, timestamp: Date.now() });
    return data;
  }
}

const cache = new APICache();

// 使用缓存
const getProjectInfo = () => cache.get('project-info', () => apiClient.project.getInfo());
```

## 📖 迁移指南

### **从旧IPC事件系统迁移**

#### **步骤1: 识别事件类型**
```typescript
// 旧代码分析
window.electronAPI.on('build-progress', handler);  // → 事件总线
window.electronAPI.invoke('build-start', options); // → 保持IPC调用
```

#### **步骤2: 替换事件监听**
```typescript
// ❌ 旧方式
window.electronAPI.on('build-progress', (data) => {
  updateProgress(data);
});

// ✅ 新方式
const unsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
  updateProgress(data);
});

// 记得清理
onUnmounted(() => {
  unsubscriber();
});
```

#### **步骤3: 保持IPC调用不变**
```typescript
// ✅ 这些保持不变
const result = await window.electronAPI.invoke('build-start', options);
const history = await window.electronAPI.invoke('build-get-history');
```

### **渐进式迁移策略**
1. **第一阶段**: 新功能使用新系统
2. **第二阶段**: 迁移高频事件（构建进度、版本变更）
3. **第三阶段**: 迁移其他事件
4. **第四阶段**: 清理旧代码

## 🎓 学习资源

### **相关文档**
- [EventBus-Migration-Guide.md](./EventBus-Migration-Guide.md) - 迁移指南
- [EventBus-Best-Practices.md](./EventBus-Best-Practices.md) - 最佳实践
- [API参考文档](../src/shared/events/EventBusExample.ts) - 代码示例

### **调试工具**
- 浏览器开发者工具 - 查看事件日志
- Electron DevTools - 监控IPC通信
- 事件总线统计API - 监控订阅状态

### **常见问题解答**

**Q: 什么时候使用事件总线，什么时候使用IPC调用？**
A: 简单记忆：需要结果用IPC，推送状态用事件总线。

**Q: 事件监听器会导致内存泄漏吗？**
A: 如果不正确清理会导致内存泄漏。务必在组件卸载时调用unsubscriber。

**Q: 可以在Pinia store中使用onUnmounted吗？**
A: 不可以。onUnmounted只在Vue组件中有效。Store应该暴露清理方法供组件调用。

**Q: 如何调试事件没有被接收到？**
A: 检查事件名称是否正确，监听器是否正确设置，以及是否有过滤条件阻止了事件处理。

---

📝 **文档版本**: v1.0
🕒 **最后更新**: 2024-01-XX
👥 **维护者**: Version-Craft Team
