# 🚀 事件总线 vs IPC调用 - 快速参考卡

## 🎯 选择决策树

```
需要从渲染进程获取数据或执行操作？
├─ 是 → 使用 IPC调用 (electronAPI.invoke)
└─ 否 → 需要从主进程推送状态更新？
   ├─ 是 → 使用 事件总线 (EventBus)
   └─ 否 → 重新评估需求
```

## ⚡ 快速对比

| 特征 | 事件总线 | IPC调用 |
|------|----------|---------|
| **方向** | 主进程 → 渲染进程 | 双向通信 |
| **返回值** | 无 | 有 |
| **用途** | 状态通知 | 数据查询/操作 |
| **示例** | 构建进度更新 | 启动构建 |

## 📋 常用场景速查

### ✅ 使用事件总线的场景
- 📊 构建/部署进度更新
- 🏷️ 版本变更通知
- ⚙️ 配置变更通知
- 📝 日志推送
- 🔔 状态变更广播

### ✅ 使用IPC调用的场景
- 🚀 启动构建/部署
- 📁 文件/文件夹选择
- 💾 保存/加载配置
- 📊 查询历史数据
- ❌ 取消操作

## 🔧 代码模板

### 事件总线模板

#### 主进程发布
```typescript
import { mainEventBus } from '@/main/events/MainEventBus';

// 发布进度事件
mainEventBus.publishBuildProgress({
  buildId: 'build-123',
  platform: 'web-mobile',
  progress: 50,
  message: '正在编译...',
  timestamp: new Date().toISOString()
});
```

#### 渲染进程订阅
```typescript
import { rendererEventBus } from '@shared/events/RendererEventBus';

// 在组件中订阅
const unsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
  if (data.buildId === targetBuildId) {
    updateProgress(data.progress);
  }
});

// 清理订阅
onUnmounted(() => {
  unsubscriber();
});
```

### IPC调用模板

#### 渲染进程调用
```typescript
import { apiClient } from '@shared/api/VersionCraftAPIClient';

// 启动操作
const result = await apiClient.build.start({
  platform: 'web-mobile',
  clean: true
});

// 查询数据
const history = await apiClient.build.getHistory();
```

#### 主进程处理
```typescript
import { ipcMain } from 'electron';

ipcMain.handle('build-start', async (event, options) => {
  const result = await buildService.startBuild(options);
  return result;
});
```

## 🎨 组合使用模式

### 模式1: 操作-监听
```typescript
// 1. IPC启动操作
const { buildId } = await apiClient.build.start(options);

// 2. 事件总线监听进度
const progressSub = rendererEventBus.subscribeBuildProgress((data) => {
  if (data.buildId === buildId) updateProgress(data);
});

// 3. 事件总线监听完成
const completeSub = rendererEventBus.subscribeBuildComplete((data) => {
  if (data.buildId === buildId) {
    handleComplete(data);
    progressSub();
    completeSub();
  }
});
```

### 模式2: 查询-缓存-监听
```typescript
// 1. IPC查询初始数据
const initialData = await apiClient.project.getInfo();
updateUI(initialData);

// 2. 事件总线监听变更
const unsubscriber = rendererEventBus.subscribeVersionChanged(() => {
  // 重新查询数据
  apiClient.project.getInfo().then(updateUI);
});
```

## ⚠️ 常见错误

### ❌ 错误用法
```typescript
// 错误1: 用事件总线做请求响应
mainEventBus.publish('get-data'); // 无法获取返回值

// 错误2: 用IPC做实时推送
setInterval(() => {
  apiClient.getProgress(); // 轮询，效率低
}, 1000);

// 错误3: 忘记清理监听器
rendererEventBus.subscribe(handler); // 内存泄漏
```

### ✅ 正确用法
```typescript
// 正确1: IPC获取数据
const data = await apiClient.getData();

// 正确2: 事件总线推送
mainEventBus.publishProgress(data);

// 正确3: 正确清理
const unsubscriber = rendererEventBus.subscribe(handler);
onUnmounted(() => unsubscriber());
```

## 🔍 调试技巧

### 事件总线调试
```typescript
// 查看事件统计
console.log('事件统计:', rendererEventBus.getEventStats());

// 查看订阅统计
console.log('订阅统计:', rendererEventBus.getSubscriptionStats());
```

### IPC调用调试
```typescript
// 在开发者工具中查看
console.log('IPC调用:', channel, args);
```

## 📊 性能提示

### 事件总线优化
- ✅ 早期过滤不相关事件
- ✅ 使用防抖处理高频事件
- ✅ 及时清理监听器
- ❌ 避免在监听器中执行重操作

### IPC调用优化
- ✅ 使用Promise.all并行调用
- ✅ 缓存不常变的数据
- ✅ 批量处理多个操作
- ❌ 避免频繁的小数据调用

## 🎓 记忆口诀

**"推拉原则"**
- 📤 **推送状态** → 事件总线
- 📥 **拉取数据** → IPC调用

**"单双原则"**
- 📡 **单向通知** → 事件总线
- 🔄 **双向交互** → IPC调用

**"时效原则"**
- ⚡ **实时更新** → 事件总线
- 🎯 **按需获取** → IPC调用

## 🔗 相关文档

- 📖 [完整使用指南](./EventBus-vs-IPC-Guide.md)
- 🔄 [迁移指南](./EventBus-Migration-Guide.md)
- 🎯 [最佳实践](./EventBus-Best-Practices.md)
- 💡 [代码示例](../src/shared/events/EventBusExample.ts)

---
💡 **提示**: 当不确定使用哪种方式时，问自己："我需要返回值吗？" 需要就用IPC，不需要就用事件总线。
