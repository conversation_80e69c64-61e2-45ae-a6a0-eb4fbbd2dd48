# 📚 Version-Craft GUI 文档中心

欢迎来到 Version-Craft GUI 的文档中心！这里包含了所有关于事件总线系统、IPC通信和最佳实践的详细文档。

## 🎯 快速导航

### 🚀 新手入门
- [📋 快速参考卡](./Quick-Reference-Card.md) - 5分钟掌握核心概念
- [🎯 使用场景对比](./EventBus-vs-IPC-Guide.md#使用场景对比表) - 什么时候用什么

### 📖 详细指南
- [🔄 事件总线 vs IPC调用完整指南](./EventBus-vs-IPC-Guide.md) - 深入理解两种通信方式
- [🔄 迁移指南](./EventBus-Migration-Guide.md) - 从旧系统迁移到新系统
- [🎯 最佳实践](./EventBus-Best-Practices.md) - 避免常见错误和内存泄漏

## 📋 文档概览

| 文档 | 用途 | 适合人群 | 阅读时间 |
|------|------|----------|----------|
| [Quick-Reference-Card.md](./Quick-Reference-Card.md) | 快速查阅 | 所有开发者 | 5分钟 |
| [EventBus-vs-IPC-Guide.md](./EventBus-vs-IPC-Guide.md) | 完整指南 | 新手和进阶 | 30分钟 |
| [EventBus-Migration-Guide.md](./EventBus-Migration-Guide.md) | 迁移指导 | 维护现有代码 | 15分钟 |
| [EventBus-Best-Practices.md](./EventBus-Best-Practices.md) | 最佳实践 | 进阶开发者 | 20分钟 |

## 🎨 架构概览

```mermaid
graph TD
    A[渲染进程 UI] --> B[IPC调用]
    B --> C[主进程服务]
    C --> D[事件总线]
    D --> A
    
    B -.->|请求响应| C
    D -.->|状态通知| A
    
    style B fill:#FFE4B5
    style D fill:#E6FFE6
```

## 🔧 核心概念

### 事件总线 (EventBus)
- **方向**: 主进程 → 渲染进程
- **用途**: 实时状态通知、进度更新
- **特点**: 异步、一对多、发布订阅

### IPC调用 (electronAPI.invoke)
- **方向**: 渲染进程 ↔ 主进程
- **用途**: 数据查询、操作执行
- **特点**: 同步/异步、一对一、请求响应

## 🚀 快速开始

### 1. 发布事件（主进程）
```typescript
import { mainEventBus } from '@/main/events/MainEventBus';

mainEventBus.publishBuildProgress({
  buildId: 'build-123',
  progress: 50,
  message: '正在编译...',
  timestamp: new Date().toISOString()
});
```

### 2. 监听事件（渲染进程）
```typescript
import { rendererEventBus } from '@shared/events/RendererEventBus';

const unsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
  console.log('构建进度:', data.progress);
});

// 清理监听器
onUnmounted(() => unsubscriber());
```

### 3. IPC调用（渲染进程）
```typescript
import { apiClient } from '@shared/api/VersionCraftAPIClient';

const result = await apiClient.build.start({
  platform: 'web-mobile',
  clean: true
});
```

## 📊 使用场景速查

| 场景 | 推荐方案 | 文档链接 |
|------|----------|----------|
| 启动构建 | IPC调用 | [示例](./EventBus-vs-IPC-Guide.md#示例1-构建流程完整实现) |
| 构建进度 | 事件总线 | [示例](./EventBus-vs-IPC-Guide.md#示例1-构建流程完整实现) |
| 获取历史 | IPC调用 | [示例](./EventBus-vs-IPC-Guide.md#示例2-项目信息管理) |
| 版本通知 | 事件总线 | [示例](./EventBus-vs-IPC-Guide.md#示例3-全局状态管理) |

## ⚠️ 常见问题

### Q: 什么时候使用事件总线？
A: 当主进程需要向渲染进程推送实时状态更新时，如构建进度、版本变更等。

### Q: 什么时候使用IPC调用？
A: 当渲染进程需要从主进程获取数据或执行操作时，如启动构建、查询历史等。

### Q: 如何避免内存泄漏？
A: 务必在组件卸载时调用事件监听器的 `unsubscriber()` 函数。

### Q: 可以在Pinia store中使用onUnmounted吗？
A: 不可以。`onUnmounted` 只在Vue组件中有效。详见[最佳实践文档](./EventBus-Best-Practices.md)。

## 🔍 调试工具

### 事件总线调试
```typescript
// 查看事件统计
console.log(rendererEventBus.getEventStats());

// 查看订阅统计
console.log(rendererEventBus.getSubscriptionStats());
```

### IPC调用调试
在浏览器开发者工具中查看网络面板或使用自定义日志包装器。

## 🎓 学习路径

### 初学者
1. 阅读 [快速参考卡](./Quick-Reference-Card.md)
2. 查看 [基础示例](./EventBus-vs-IPC-Guide.md#实际代码示例)
3. 实践简单的事件监听

### 进阶开发者
1. 深入理解 [完整指南](./EventBus-vs-IPC-Guide.md)
2. 学习 [最佳实践](./EventBus-Best-Practices.md)
3. 掌握性能优化技巧

### 维护者
1. 了解 [迁移指南](./EventBus-Migration-Guide.md)
2. 掌握调试和监控技巧
3. 建立代码审查标准

## 🔗 相关资源

### 代码示例
- [EventBusExample.ts](../src/shared/events/EventBusExample.ts) - 完整代码示例
- [useBuildEvents.ts](../src/renderer/composables/useBuildEvents.ts) - Vue Composable示例

### 核心文件
- [MainEventBus.ts](../src/main/events/MainEventBus.ts) - 主进程事件总线
- [RendererEventBus.ts](../src/shared/events/RendererEventBus.ts) - 渲染进程事件总线
- [VersionCraftAPIClient.ts](../src/shared/api/VersionCraftAPIClient.ts) - IPC调用客户端

## 📝 贡献指南

### 文档更新
1. 修改相应的Markdown文件
2. 确保示例代码可运行
3. 更新相关的交叉引用

### 代码示例
1. 保持示例简洁明了
2. 添加必要的注释
3. 包含错误处理

### 最佳实践
1. 基于实际使用经验
2. 包含性能考虑
3. 提供反面教材

---

📝 **文档版本**: v1.0  
🕒 **最后更新**: 2024-01-XX  
👥 **维护者**: Version-Craft Team  
📧 **反馈**: 如有问题或建议，请提交Issue或PR
