@echo off
echo ===== Version-Craft GUI Fix and Build =====
echo.

echo [INFO] Fixing package.json dependencies...
echo electron has been moved from dependencies to devDependencies
echo.

echo [INFO] Cleaning node_modules and package-lock.json...
if exist "node_modules" (
    rmdir /s /q "node_modules"
    echo Removed node_modules
)
if exist "package-lock.json" (
    del "package-lock.json"
    echo Removed package-lock.json
)
echo.

echo [INFO] Cleaning previous build files...
if exist "dist" (
    rmdir /s /q "dist"
    echo Removed dist folder
)
if exist "release" (
    rmdir /s /q "release"
    echo Removed release folder
)
echo.

echo [INFO] Installing dependencies with correct configuration...
npm install
if errorlevel 1 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)
echo [SUCCESS] Dependencies installed correctly
echo.

echo [INFO] Building main process...
npm run build:main
if errorlevel 1 (
    echo [ERROR] Main process build failed
    pause
    exit /b 1
)
echo [SUCCESS] Main process built
echo.

echo [INFO] Building preload script...
npm run build:preload
if errorlevel 1 (
    echo [ERROR] Preload script build failed
    pause
    exit /b 1
)
echo [SUCCESS] Preload script built
echo.

echo [INFO] Building renderer process...
npm run build:renderer
if errorlevel 1 (
    echo [ERROR] Renderer process build failed
    pause
    exit /b 1
)
echo [SUCCESS] Renderer process built
echo.

echo [INFO] Packaging to Windows executable...
npm run dist:win
if errorlevel 1 (
    echo [ERROR] Packaging failed
    pause
    exit /b 1
)

echo.
echo ===== BUILD COMPLETED SUCCESSFULLY =====
echo.
echo Output files are in the release/ folder
echo.

if exist "release" (
    echo Opening release folder...
    start "" "release"
) else (
    echo Warning: release folder not found
)

echo.
echo Press any key to exit...
pause >nul
