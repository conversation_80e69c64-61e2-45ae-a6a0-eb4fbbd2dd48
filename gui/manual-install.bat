@echo off
echo ===== Manual Dependency Installation =====
echo.

echo Step 1: Clean everything
if exist "node_modules" rmdir /s /q "node_modules"
if exist "package-lock.json" del "package-lock.json"
npm cache clean --force

echo Step 2: Install core dependencies first
npm install @version-craft/core@file:../packages/core
npm install chokidar@^3.5.3
npm install electron-store@^8.1.0
npm install fs-extra@^11.1.1
npm install semver@^7.5.4
npm install simple-git@^3.20.0

echo Step 3: Install dev dependencies
npm install --save-dev @heroicons/vue@^2.0.18
npm install --save-dev @types/node@^20.8.0
npm install --save-dev @vitejs/plugin-vue@^4.4.0
npm install --save-dev @vue/tsconfig@^0.8.1
npm install --save-dev autoprefixer@^10.4.16
npm install --save-dev concurrently@^8.2.2
npm install --save-dev date-fns@^2.30.0

echo Step 4: Install electron and electron-builder
npm install --save-dev electron@25.9.0
npm install --save-dev electron-builder@24.6.4

echo Step 5: Install remaining dev dependencies
npm install --save-dev glob@^11.0.3
npm install --save-dev minimatch@^10.0.3
npm install --save-dev pinia@^2.1.7
npm install --save-dev postcss@^8.4.31
npm install --save-dev tailwindcss@^3.3.5
npm install --save-dev terser@^5.43.1
npm install --save-dev typescript@^5.2.2
npm install --save-dev vite@^4.5.0
npm install --save-dev vite-plugin-checker@^0.10.3
npm install --save-dev vue@^3.3.8
npm install --save-dev vue-router@^4.2.5
npm install --save-dev vue-tsc@^3.0.6

echo Step 6: Verify installation
echo Checking electron...
npx electron --version

echo Checking electron-builder...
npx electron-builder --version

echo.
echo Manual installation completed!
echo Now you can run: npm run build followed by npm run dist:win
echo.
pause
