@echo off
cd /d "%~dp0"
echo Quick Fix for Electron Build Issue
echo.

echo Removing problematic files...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json

echo Installing with fixed versions...
npm install --no-package-lock

echo Testing electron...
node -e "console.log('Electron version:', require('electron/package.json').version)"

echo Building and packaging...
npm run build
npm run dist:win

pause
