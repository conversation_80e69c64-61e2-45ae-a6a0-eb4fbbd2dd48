/**
 * 主进程事件总线
 * 统一管理所有服务的事件发布，简化事件传递链路
 * 复用现有的 IPC_COMMANDS.EVENTS 常量，避免重复定义
 */

import { EventEmitter } from 'events';
import { IPC_COMMANDS, EVENT_NAMES } from '../../shared/constants/ipc-commands';
import type { 
  BuildResult, 
  BuildOptions,
  DeployResult,
  VersionInfo,
  APIResponse 
} from '../../shared/types/api';

// ==================== 事件数据类型定义 ====================
// 复用 shared/types/api.ts 中的类型，避免重复定义

export interface BuildProgressEvent {
  buildId: string;
  platform: string;
  progress: number;
  message: string;
  elapsed?: number;
  timestamp: string;
}

export interface BuildCompleteEvent {
  buildId: string;
  platform: string;
  success: boolean;
  result?: BuildResult;
  error?: string;
  timestamp: string;
}

export interface VersionChangedEvent {
  oldVersion: string;
  newVersion: string;
  type: 'major' | 'minor' | 'patch' | 'prerelease';
  timestamp: string;
}

export interface DeployProgressEvent {
  deploymentId: string;
  environment: string;
  progress: number;
  message: string;
  timestamp: string;
}

export interface DeployCompleteEvent {
  deploymentId: string;
  environment: string;
  success: boolean;
  result?: DeployResult;
  error?: string;
  timestamp: string;
}

// 统一的事件数据类型
export type EventData = 
  | BuildProgressEvent
  | BuildCompleteEvent
  | VersionChangedEvent
  | DeployProgressEvent
  | DeployCompleteEvent;

// ==================== 主进程事件总线 ====================

class MainEventBus extends EventEmitter {
  private static instance: MainEventBus | null = null;
  private eventStats = new Map<string, number>();
  private lastEventTime = new Map<string, number>();

  private constructor() {
    super();
    this.setMaxListeners(50); // 增加最大监听器数量
    this.setupDebugLogging();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): MainEventBus {
    if (!MainEventBus.instance) {
      MainEventBus.instance = new MainEventBus();
    }
    return MainEventBus.instance;
  }

  // ==================== 构建事件发布 ====================

  /**
   * 发布构建进度事件
   */
  publishBuildProgress(data: BuildProgressEvent): void {
    this.publishEvent(EVENT_NAMES.BUILD_PROGRESS, data);
  }

  /**
   * 发布构建完成事件
   */
  publishBuildComplete(data: BuildCompleteEvent): void {
    this.publishEvent(EVENT_NAMES.BUILD_COMPLETE, data);
  }

  /**
   * 发布构建日志事件
   */
  publishBuildLog(buildId: string, level: string, message: string): void {
    this.publishEvent(EVENT_NAMES.BUILD_LOG, {
      buildId,
      level,
      message,
      timestamp: new Date().toISOString()
    });
  }

  // ==================== 版本事件发布 ====================

  /**
   * 发布版本变更事件
   */
  publishVersionChanged(data: VersionChangedEvent): void {
    this.publishEvent(EVENT_NAMES.VERSION_CHANGED, data);
  }

  // ==================== 部署事件发布 ====================

  /**
   * 发布部署进度事件
   */
  publishDeployProgress(data: DeployProgressEvent): void {
    this.publishEvent(EVENT_NAMES.DEPLOY_PROGRESS, data);
  }

  /**
   * 发布部署完成事件
   */
  publishDeployComplete(data: DeployCompleteEvent): void {
    this.publishEvent(EVENT_NAMES.DEPLOY_COMPLETE, data);
  }

  // ==================== 配置事件发布 ====================

  /**
   * 发布配置变更事件
   */
  publishConfigChanged(configType: string, changes: any): void {
    this.publishEvent(EVENT_NAMES.CONFIG_CHANGED, {
      configType,
      changes,
      timestamp: new Date().toISOString()
    });
  }

  // ==================== 核心事件发布方法 ====================

  /**
   * 统一的事件发布方法
   */
  private publishEvent(eventName: string, data: any): void {
    try {
      // 防重复发送检查（100ms内的相同事件视为重复）
      const eventKey = `${eventName}:${JSON.stringify(data)}`;
      const now = Date.now();
      const lastTime = this.lastEventTime.get(eventKey) || 0;
      
      if (now - lastTime < 100) {
        console.log(`🔄 [MainEventBus] Skipping duplicate event: ${eventName}`);
        return;
      }
      
      this.lastEventTime.set(eventKey, now);

      // 更新事件统计
      const currentCount = this.eventStats.get(eventName) || 0;
      this.eventStats.set(eventName, currentCount + 1);

      // 发布事件
      console.log(`📡 [MainEventBus] Publishing event: ${eventName}`, data);
      this.emit(eventName, data);

      // 发布通用事件（用于全局监听）
      this.emit('*', eventName, data);

    } catch (error) {
      console.error(`❌ [MainEventBus] Error publishing event ${eventName}:`, error);
    }
  }

  // ==================== 事件监听管理 ====================

  /**
   * 监听特定事件
   */
  subscribe(eventName: string, callback: (data: any) => void): () => void {
    console.log(`🎧 [MainEventBus] Subscribing to event: ${eventName}`);
    this.on(eventName, callback);
    
    // 返回取消订阅函数
    return () => {
      console.log(`🔇 [MainEventBus] Unsubscribing from event: ${eventName}`);
      this.off(eventName, callback);
    };
  }

  /**
   * 监听所有事件
   */
  subscribeAll(callback: (eventName: string, data: any) => void): () => void {
    console.log(`🎧 [MainEventBus] Subscribing to all events`);
    this.on('*', callback);
    
    return () => {
      console.log(`🔇 [MainEventBus] Unsubscribing from all events`);
      this.off('*', callback);
    };
  }

  // ==================== 调试和统计 ====================

  /**
   * 获取事件统计
   */
  getEventStats(): Map<string, number> {
    return new Map(this.eventStats);
  }

  /**
   * 清除事件统计
   */
  clearEventStats(): void {
    this.eventStats.clear();
    this.lastEventTime.clear();
  }

  /**
   * 设置调试日志
   */
  private setupDebugLogging(): void {
    // 监听所有事件进行调试
    this.on('*', (eventName: string, data: any) => {
      const count = this.eventStats.get(eventName) || 0;
      console.log(`📊 [MainEventBus] Event ${eventName} published (count: ${count})`);
    });

    // 监听错误
    this.on('error', (error: Error) => {
      console.error('❌ [MainEventBus] EventEmitter error:', error);
    });
  }

  /**
   * 销毁事件总线
   */
  destroy(): void {
    this.removeAllListeners();
    this.eventStats.clear();
    this.lastEventTime.clear();
    MainEventBus.instance = null;
  }
}

// 导出单例实例
export const mainEventBus = MainEventBus.getInstance();
export default mainEventBus;
