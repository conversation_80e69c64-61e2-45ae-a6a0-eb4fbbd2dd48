import { app, BrowserWindow, ipcMain, dialog, shell } from 'electron';
import { join } from 'path';
import Store from 'electron-store';
import fs from 'fs-extra';
import { VersionCraftService } from './services/VersionCraftService';
import { ProjectService } from './services/ProjectService';
import { IPCHandlers } from './ipc/handlers';
import { IPC_COMMANDS } from '../shared/constants/ipc-commands';
import { mainEventBus } from './events/MainEventBus';

// 配置存储
const store = new Store();

// 服务实例
let versionCraftService: VersionCraftService;
let projectService: ProjectService;

// 主窗口
let mainWindow: BrowserWindow | null = null;

// 开发环境检测
const isDev = process.env.NODE_ENV === 'development';

// 创建主窗口
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: join(__dirname, './preload.js')
    },
    titleBarStyle: 'default',
    show: false, // 先隐藏，加载完成后显示
    icon: join(__dirname, '../../assets/icon.png') // 应用图标
  });

  // 加载页面
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    // 在生产环境中，__dirname 指向 dist/main，所以需要向上一级到达 dist，然后进入 renderer
    const htmlPath = join(__dirname, '../renderer/index.html');
    console.log('Loading HTML from:', htmlPath);
    mainWindow.loadFile(htmlPath);
  }

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    console.log('✅ [Main] 窗口准备就绪');
    mainWindow?.show();

    // 恢复窗口状态
    const windowState = store.get('windowState') as any;
    if (windowState) {
      mainWindow?.setBounds(windowState);
      if (windowState.isMaximized) {
        mainWindow?.maximize();
      }
    }
  });

  // 监听 preload 脚本加载
  mainWindow.webContents.once('did-finish-load', () => {
    console.log('✅ [Main] 页面加载完成');

    // 延迟一点时间再检查，确保 preload 脚本有时间执行
    setTimeout(() => {
      // 验证 preload 脚本是否正确加载
      mainWindow?.webContents.executeJavaScript('typeof window.electronAPI !== "undefined"')
        .then(hasAPI => {
          if (hasAPI) {
            console.log('✅ [Main] electronAPI 已正确暴露到渲染进程');
          } else {
            console.error('❌ [Main] electronAPI 未能暴露到渲染进程');

            // 进一步调试
            mainWindow?.webContents.executeJavaScript('Object.keys(window)')
              .then((keys: string[]) => {
                console.log('🔍 [Main] window 对象的属性:', keys.filter(k => k.includes('electron') || k.includes('API')));
              });
          }
        })
        .catch(error => {
          console.error('❌ [Main] 检查 electronAPI 时出错:', error);
        });
    }, 1000);
  });

  // 监听 preload 脚本错误
  mainWindow.webContents.on('preload-error', (event, preloadPath, error) => {
    console.error('❌ [Main] Preload 脚本加载错误:', preloadPath, error);
  });

  // 保存窗口状态
  mainWindow.on('close', () => {
    if (mainWindow) {
      const bounds = mainWindow.getBounds();
      const isMaximized = mainWindow.isMaximized();
      store.set('windowState', { ...bounds, isMaximized });
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// 应用准备就绪
app.whenReady().then(() => {
  createMainWindow();

  // 注册基础的 IPC 处理器（项目管理、系统操作等）
  IPCHandlers.registerBasicHandlers();

  // 初始化服务（如果有保存的项目路径）
  const currentProject = store.get('currentProject') as string;
  console.log('Current project:', currentProject);
  if (currentProject && fs.existsSync(currentProject)) {
    versionCraftService = new VersionCraftService(currentProject);
    projectService = new ProjectService(currentProject);

    // 注册项目相关的 IPC 处理器，传递 ProjectService
    IPCHandlers.register(versionCraftService, projectService);
  }

  // macOS 特殊处理
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

// 所有窗口关闭时退出应用 (macOS 除外)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
