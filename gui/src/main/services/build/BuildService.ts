import { BuildManager } from '@version-craft/core';
import { ChildProcess } from 'child_process';
import { mainEventBus } from '../../events/MainEventBus';
import type { BuildProgressEvent, BuildCompleteEvent } from '../../events/MainEventBus';

/**
 * 构建管理服务
 * 负责所有构建相关的操作
 */
export class BuildService {
  private buildManager: BuildManager;
  private projectPath: string;
  private runningProcesses: Map<string, any> = new Map();

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    // 设置工作目录并初始化构建管理器
    process.chdir(projectPath);
    this.buildManager = new BuildManager();
  }

  /**
   * 开始构建 - 使用核心包
   */
  async startBuild(platform: string, options: any = {}): Promise<any> {
    const buildId = options.buildId || `build_${Date.now()}_${platform}`;
    const startTime = new Date().toISOString();

    console.log('✅ [BuildService] Starting build using core package for platform:', platform, buildId);

    // 发送构建开始进度事件
    this.notifyProgress(buildId, { progress: 0, message: '开始构建...', platform });

    try {
      // 创建一个可取消的构建 Promise
      const buildPromise = this.buildManager.buildPlatform(platform);

      // 将构建 Promise 包装为可取消的进程
      const cancellableProcess = this.createCancellableProcess(buildPromise, buildId);

      // 将进程添加到运行中的进程列表
      this.runningProcesses.set(buildId, cancellableProcess);

      // 启动进度模拟器（因为核心包暂时无法提供实时进度回调）
      const progressTimer = this.startProgressSimulator(buildId, platform, new Date(startTime));

      try {
        // 等待构建完成
        const buildResult = await buildPromise;

        // 停止进度模拟器
        this.stopProgressSimulator(progressTimer);

        // 构建完成后从运行列表中移除
        this.runningProcesses.delete(buildId);

        console.log('✅ [BuildService] Build completed:', buildResult);

        const endTime = new Date().toISOString();
        const duration = Date.now() - new Date(startTime).getTime();

        const result = {
          buildId,
          platform,
          startTime,
          endTime,
          duration,
          success: buildResult.success,
          outputPath: buildResult.outputPath,
          buildSize: buildResult.fileSize,
          message: buildResult.success ? '构建成功完成' : '构建失败',
          error: buildResult.error
        };

        // 发送构建完成事件
        this.notifyComplete(buildId, buildResult.success, buildResult.error);

        return result;

      } catch (error) {
        // 停止进度模拟器
        this.stopProgressSimulator(progressTimer);
        throw error;
      }

    } catch (error) {
      // 构建失败或被取消时也要清理进程记录
      this.runningProcesses.delete(buildId);

      console.error('❌ [BuildService] Build error:', error);

      const endTime = new Date().toISOString();
      const duration = Date.now() - new Date(startTime).getTime();
      const errorMessage = error instanceof Error ? error.message : String(error);
      const isCancelled = errorMessage.includes('cancelled');

      const result = {
        buildId,
        platform,
        startTime,
        endTime,
        duration,
        success: false,
        message: isCancelled ? '构建已取消' : '构建失败',
        error: errorMessage
      };

      // 发送构建完成事件（失败或取消）
      this.notifyComplete(buildId, false, errorMessage);

      return result;
    }
  }

  /**
   * 构建指定平台 - 使用核心包
   */
  async buildPlatform(platform: string, options: any = {}): Promise<any> {
    console.log(`✅ [BuildService] Building ${platform} platform using core package...`);
    return this.startBuild(platform, options);
  }

  /**
   * 构建所有平台 - 使用核心包
   */
  async buildAllPlatforms(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building all platforms using core package...');

    try {
      const buildResults = await this.buildManager.buildAllPlatforms();
      console.log('✅ [BuildService] All platforms build completed:', buildResults);
      
      const successfulBuilds = buildResults.filter(result => result.success);
      
      return {
        message: `构建完成: ${successfulBuilds.length}/${buildResults.length} 成功`,
        results: buildResults,
        summary: {
          total: buildResults.length,
          successful: successfulBuilds.length,
          failed: buildResults.length - successfulBuilds.length
        }
      };

    } catch (error) {
      console.error('❌ [BuildService] Error building all platforms:', error);
      throw new Error(`构建所有平台失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取构建统计信息 - 使用核心包
   */
  async getBuildStats(): Promise<any> {
    console.log('✅ [BuildService] Getting build stats using core package...');

    try {
      const buildStats = await this.buildManager.getBuildStats();
      console.log('✅ [BuildService] Got build stats:', buildStats);
      return buildStats;
    } catch (error) {
      console.error('❌ [BuildService] Error getting build stats:', error);
      throw new Error(`获取构建统计失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 清理构建输出 - 使用核心包
   */
  async cleanBuild(): Promise<any> {
    console.log('✅ [BuildService] Cleaning build using core package...');

    try {
      await this.buildManager.cleanBuildOutput();
      console.log('✅ [BuildService] Build cleaned successfully');
      return {
        message: '构建输出清理成功'
      };
    } catch (error) {
      console.error('❌ [BuildService] Error cleaning build:', error);
      throw new Error(`清理构建失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 构建特定平台（Web Mobile）- 使用核心包
   */
  async buildWeb(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building web platform using core package...');
    return this.startBuild('web-mobile', options);
  }

  /**
   * 构建特定平台（Android）- 使用核心包
   */
  async buildAndroid(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building android platform using core package...');
    return this.startBuild('android', options);
  }

  /**
   * 构建 Android 并签名 - 对应 CLI 的 build-android --sign
   */
  async buildAndroidSigned(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building and signing android using core package...');
    const buildOptions = { ...options, sign: true };
    return this.startBuild('android', buildOptions);
  }

  /**
   * 构建特定平台（iOS）- 使用核心包
   */
  async buildIOS(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building ios platform using core package...');
    return this.startBuild('ios', options);
  }

  /**
   * 构建特定平台（Windows）- 使用核心包
   */
  async buildWindows(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building windows platform using core package...');
    return this.startBuild('windows', options);
  }

  /**
   * 构建特定平台（Mac）- 使用核心包
   */
  async buildMac(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building mac platform using core package...');
    return this.startBuild('mac', options);
  }

  /**
   * 发送构建进度通知 - 直接使用事件总线
   */
  private notifyProgress(buildId: string, data: any): void {
    // 直接通过事件总线发布事件
    const eventData: BuildProgressEvent = {
      buildId,
      platform: data.platform || '',
      progress: data.progress || 0,
      message: data.message || '',
      elapsed: data.elapsed || 0,
      timestamp: new Date().toISOString()
    };

    console.log('📡 [BuildService] Publishing BUILD_PROGRESS event via EventBus:', eventData);
    mainEventBus.publishBuildProgress(eventData);
  }

  /**
   * 发送构建完成通知 - 直接使用事件总线
   */
  private notifyComplete(buildId: string, success: boolean, error?: string, platform?: string): void {
    // 直接通过事件总线发布事件
    const eventData: BuildCompleteEvent = {
      buildId,
      platform: platform || '',
      success,
      error: error || undefined,
      timestamp: new Date().toISOString()
    };

    console.log('📡 [BuildService] Publishing BUILD_COMPLETE event via EventBus:', eventData);
    mainEventBus.publishBuildComplete(eventData);
  }

  /**
   * 启动进度模拟器
   * 由于核心包暂时无法提供实时进度回调，使用模拟器来提供进度更新
   */
  private startProgressSimulator(buildId: string, platform: string, startTime: Date): NodeJS.Timeout {
    let currentProgress = 5; // 从5%开始

    const timer = setInterval(() => {
      const elapsed = Date.now() - startTime.getTime();

      // 根据时间推算进度，但不超过90%
      const timeBasedProgress = Math.min(90, (elapsed / 120000) * 100); // 2分钟内到90%

      // 使用较大的值，但增长要平滑
      const targetProgress = Math.max(currentProgress, timeBasedProgress);
      currentProgress = Math.min(90, currentProgress + Math.random() * 3 + 1); // 每次增长1-4%

      const progress = Math.min(currentProgress, targetProgress);

      // 根据进度阶段提供不同的消息
      let message = '正在构建...';
      if (progress < 20) {
        message = '准备构建环境...';
      } else if (progress < 40) {
        message = '编译资源文件...';
      } else if (progress < 60) {
        message = '打包应用程序...';
      } else if (progress < 80) {
        message = '优化构建输出...';
      } else {
        message = '完成最后步骤...';
      }

      this.notifyProgress(buildId, {
        progress: Math.round(progress),
        message,
        platform,
        elapsed
      });

    }, 2000); // 每2秒更新一次

    return timer;
  }

  /**
   * 停止进度模拟器
   */
  private stopProgressSimulator(timer: NodeJS.Timeout): void {
    if (timer) {
      clearInterval(timer);
    }
  }

  /**
   * 创建可取消的进程包装器
   */
  private createCancellableProcess(buildPromise: Promise<any>, buildId: string): any {
    // 创建一个虚拟的进程对象来管理取消状态
    let isKilled = false;

    const mockProcess = {
      killed: false,
      pid: Date.now(), // 使用时间戳作为虚拟 PID
      kill: (signal?: string) => {
        console.log(`🛑 [BuildService] Cancelling build ${buildId} with signal: ${signal}`);
        isKilled = true;
        // 更新 killed 状态的 getter
        Object.defineProperty(mockProcess, 'killed', {
          value: true,
          writable: false,
          configurable: true
        });
        // 这里可以添加实际的取消逻辑，比如设置取消标志
        return true;
      }
    };

    return mockProcess;
  }

  /**
   * 取消构建
   */
  cancelBuild(buildId: string): boolean {
    console.log(`🛑 [BuildService] Attempting to cancel build: ${buildId}`);

    const process = this.runningProcesses.get(buildId);
    if (process) {
      try {
        // 首先尝试优雅地终止进程
        process.kill('SIGTERM');

        // 设置超时，如果进程没有在5秒内终止，则强制杀死
        setTimeout(() => {
          if (!process.killed) {
            console.log(`⚠️ [BuildService] Force killing build process: ${buildId}`);
            process.kill('SIGKILL');
          }
        }, 5000);

        this.runningProcesses.delete(buildId);

        // 发送构建取消事件
        this.notifyComplete(buildId, false, '构建已取消');

        console.log(`✅ [BuildService] Build ${buildId} cancellation initiated`);
        return true;
      } catch (error) {
        console.error(`❌ [BuildService] Error cancelling build ${buildId}:`, error);
        // 即使出错也要清理进程记录
        this.runningProcesses.delete(buildId);
        return false;
      }
    } else {
      console.log(`⚠️ [BuildService] Build ${buildId} not found in running processes`);
      return false;
    }
  }

  /**
   * 获取支持的平台列表
   */
  getSupportedPlatforms(): string[] {
    return ['web-mobile', 'android', 'ios', 'windows', 'mac'];
  }

  /**
   * 检查平台是否支持
   */
  isPlatformSupported(platform: string): boolean {
    return this.getSupportedPlatforms().includes(platform);
  }
}
