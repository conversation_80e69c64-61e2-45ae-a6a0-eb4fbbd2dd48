/**
 * tRPC Router - 统一的类型安全IPC通信
 * 替代复杂的事件传递链，提供直接的主进程到渲染进程通信
 */

import { z } from 'zod';
import { initTRPC } from '@trpc/server';
import { observable } from '@trpc/server/observable';
import { EventEmitter } from 'events';
import { VersionCraftService } from '../services/VersionCraftService';

// 创建 tRPC 实例
const t = initTRPC.create();

// 全局事件发射器，用于实时事件
const eventEmitter = new EventEmitter();

// 服务实例（需要在初始化时设置）
let versionCraftService: VersionCraftService | null = null;

export const setVersionCraftService = (service: VersionCraftService) => {
  versionCraftService = service;
};

// 构建相关的 Schema
const BuildProgressSchema = z.object({
  buildId: z.string(),
  platform: z.string(),
  progress: z.number().min(0).max(100),
  message: z.string(),
  elapsed: z.number().optional(),
  timestamp: z.string().optional(),
});

const BuildOptionsSchema = z.object({
  platform: z.string(),
  clean: z.boolean().optional(),
  buildId: z.string().optional(),
  optimization: z.object({
    minify: z.boolean().optional(),
    compress: z.boolean().optional(),
    sourcemap: z.boolean().optional(),
  }).optional(),
});

// 主路由器
export const appRouter = t.router({
  // ==================== 构建管理 ====================
  build: t.router({
    // 开始构建
    start: t.procedure
      .input(BuildOptionsSchema)
      .mutation(async ({ input }) => {
        if (!versionCraftService) {
          throw new Error('VersionCraftService not initialized');
        }

        console.log('🚀 [tRPC] Starting build:', input);
        
        try {
          const result = await versionCraftService.build.startBuild(input.platform, input);
          
          // 发送构建开始事件
          eventEmitter.emit('build:started', {
            buildId: result.buildId,
            platform: input.platform,
            timestamp: new Date().toISOString(),
          });

          return result;
        } catch (error) {
          console.error('❌ [tRPC] Build start failed:', error);
          throw error;
        }
      }),

    // 取消构建
    cancel: t.procedure
      .input(z.object({ buildId: z.string() }))
      .mutation(async ({ input }) => {
        if (!versionCraftService) {
          throw new Error('VersionCraftService not initialized');
        }

        console.log('🛑 [tRPC] Cancelling build:', input.buildId);
        
        try {
          const result = await versionCraftService.build.cancelBuild(input.buildId);
          
          // 发送构建取消事件
          eventEmitter.emit('build:cancelled', {
            buildId: input.buildId,
            timestamp: new Date().toISOString(),
          });

          return result;
        } catch (error) {
          console.error('❌ [tRPC] Build cancel failed:', error);
          throw error;
        }
      }),

    // 获取构建统计
    getStats: t.procedure
      .query(async () => {
        if (!versionCraftService) {
          throw new Error('VersionCraftService not initialized');
        }

        return await versionCraftService.build.getBuildStats();
      }),

    // 构建进度订阅 - 实时事件流
    onProgress: t.procedure
      .input(z.object({ buildId: z.string().optional() }))
      .subscription(({ input }) => {
        return observable<z.infer<typeof BuildProgressSchema>>((emit) => {
          const onProgress = (data: any) => {
            // 如果指定了 buildId，只发送匹配的事件
            if (input.buildId && data.buildId !== input.buildId) {
              return;
            }

            console.log('📡 [tRPC] Emitting build progress:', data);
            emit.next(data);
          };

          const onComplete = (data: any) => {
            // 如果指定了 buildId，只发送匹配的事件
            if (input.buildId && data.buildId !== input.buildId) {
              return;
            }

            console.log('✅ [tRPC] Emitting build complete:', data);
            emit.next({
              buildId: data.buildId,
              platform: data.platform || '',
              progress: data.success ? 100 : 0,
              message: data.success ? '构建完成' : `构建失败: ${data.error}`,
              elapsed: data.duration || 0,
              timestamp: new Date().toISOString(),
            });
          };

          // 监听事件
          eventEmitter.on('build:progress', onProgress);
          eventEmitter.on('build:complete', onComplete);

          // 清理函数
          return () => {
            eventEmitter.off('build:progress', onProgress);
            eventEmitter.off('build:complete', onComplete);
          };
        });
      }),
  }),

  // ==================== 版本管理 ====================
  version: t.router({
    // 获取当前版本
    getCurrent: t.procedure
      .query(async () => {
        if (!versionCraftService) {
          throw new Error('VersionCraftService not initialized');
        }

        return await versionCraftService.version.getCurrentVersion();
      }),

    // 版本升级
    bump: t.procedure
      .input(z.object({
        type: z.enum(['major', 'minor', 'patch', 'prerelease']),
        prerelease: z.string().optional(),
        message: z.string().optional(),
        createTag: z.boolean().optional(),
      }))
      .mutation(async ({ input }) => {
        if (!versionCraftService) {
          throw new Error('VersionCraftService not initialized');
        }

        console.log('🏷️ [tRPC] Bumping version:', input);
        
        try {
          const result = await versionCraftService.version.bumpVersion(input);
          
          // 发送版本变更事件
          eventEmitter.emit('version:changed', {
            oldVersion: result.oldVersion,
            newVersion: result.newVersion,
            type: input.type,
            timestamp: new Date().toISOString(),
          });

          return result;
        } catch (error) {
          console.error('❌ [tRPC] Version bump failed:', error);
          throw error;
        }
      }),

    // 获取版本历史
    getHistory: t.procedure
      .query(async () => {
        if (!versionCraftService) {
          throw new Error('VersionCraftService not initialized');
        }

        return await versionCraftService.version.getVersionHistory();
      }),
  }),

  // ==================== 项目管理 ====================
  project: t.router({
    // 获取项目概览
    getOverview: t.procedure
      .query(async () => {
        if (!versionCraftService) {
          throw new Error('VersionCraftService not initialized');
        }

        return await versionCraftService.getProjectOverview();
      }),

    // 健康检查
    healthCheck: t.procedure
      .query(async () => {
        if (!versionCraftService) {
          throw new Error('VersionCraftService not initialized');
        }

        return await versionCraftService.healthCheck();
      }),
  }),

  // ==================== 系统事件 ====================
  system: t.router({
    // 全局事件订阅
    onEvent: t.procedure
      .input(z.object({
        eventTypes: z.array(z.string()).optional(),
      }))
      .subscription(({ input }) => {
        return observable<any>((emit) => {
          const onEvent = (eventType: string, data: any) => {
            // 如果指定了事件类型过滤器
            if (input.eventTypes && !input.eventTypes.includes(eventType)) {
              return;
            }

            emit.next({
              type: eventType,
              data,
              timestamp: new Date().toISOString(),
            });
          };

          // 监听所有可能的事件
          const eventTypes = [
            'build:progress',
            'build:complete',
            'build:started',
            'build:cancelled',
            'version:changed',
            'deploy:progress',
            'deploy:complete',
          ];

          eventTypes.forEach(eventType => {
            eventEmitter.on(eventType, (data) => onEvent(eventType, data));
          });

          // 清理函数
          return () => {
            eventTypes.forEach(eventType => {
              eventEmitter.removeAllListeners(eventType);
            });
          };
        });
      }),
  }),
});

// 导出类型定义
export type AppRouter = typeof appRouter;

// 事件发射器导出，供服务使用
export { eventEmitter };
