<!--
  构建进度面板组件
  显示全局构建状态和各平台的构建进度
-->

<template>
  <div v-if="buildStore.isAnyBuilding || showRecentTasks" class="build-progress-panel">
    <!-- 全局构建状态 -->
    <div v-if="buildStore.isAnyBuilding" class="global-progress">
      <div class="progress-header">
        <div class="progress-info">
          <CogIcon class="w-5 h-5 text-blue-500 animate-spin" />
          <span class="progress-title">正在构建 ({{ buildStore.activeTasks.length }} 个任务)</span>
        </div>
        <div class="progress-actions">
          <button
            @click="cancelAllBuilds"
            class="btn btn-sm btn-ghost text-red-600 hover:text-red-700"
          >
            <XMarkIcon class="w-4 h-4 mr-1" />
            全部取消
          </button>
        </div>
      </div>
      
      <div class="global-progress-bar">
        <div class="progress-bar">
          <div 
            class="progress-fill"
            :style="{ width: `${buildStore.buildProgress}%` }"
          ></div>
        </div>
        <span class="progress-text">{{ buildStore.buildProgress }}%</span>
      </div>
    </div>

    <!-- 平台构建任务列表 -->
    <div class="platform-tasks">
      <div 
        v-for="task in displayTasks" 
        :key="task.id"
        class="task-item"
        :class="getTaskItemClass(task)"
      >
        <div class="task-header">
          <div class="task-info">
            <component 
              :is="getTaskIcon(task)" 
              class="w-4 h-4 mr-2"
              :class="getTaskIconClass(task)"
            />
            <span class="task-platform">{{ buildStore.getPlatformName(task.platform) }}</span>
            <span class="task-status">{{ buildStore.getStatusText(task.status) }}</span>
          </div>
          <div class="task-actions">
            <span v-if="task.duration" class="task-duration">
              {{ buildStore.formatDuration(task.duration) }}
            </span>
            <span v-else-if="task.startTime" class="task-duration">
              {{ formatElapsedTime(task.startTime) }}
            </span>
            <button
              v-if="task.status === BuildStatus.BUILDING"
              @click="cancelBuild(task.platform)"
              class="btn btn-xs btn-ghost text-red-600 hover:text-red-700"
            >
              <XMarkIcon class="w-3 h-3" />
            </button>
          </div>
        </div>

        <!-- 进度条 -->
        <div v-if="task.status === BuildStatus.BUILDING" class="task-progress">
          <div class="progress-bar">
            <div 
              class="progress-fill"
              :style="{ width: `${task.progress}%` }"
            ></div>
          </div>
          <span class="progress-text">{{ task.progress }}%</span>
        </div>

        <!-- 错误信息 -->
        <div v-if="task.error" class="task-error">
          <ExclamationCircleIcon class="w-4 h-4 text-red-500 mr-2" />
          <span class="error-text">{{ task.error }}</span>
        </div>

        <!-- 构建路径 -->
        <div v-if="task.buildPath && task.status === BuildStatus.SUCCESS" class="task-result">
          <FolderIcon class="w-4 h-4 text-green-500 mr-2" />
          <span class="result-text">{{ task.buildPath }}</span>
          <button
            @click="openBuildPath(task.buildPath)"
            class="btn btn-xs btn-ghost text-blue-600 hover:text-blue-700 ml-2"
          >
            打开
          </button>
        </div>

        <!-- 最近日志 -->
        <div v-if="showLogs && task.logs && task.logs.length > 0" class="task-logs">
          <div class="logs-header">
            <span class="logs-title">最近日志</span>
            <button
              @click="toggleLogs(task.platform)"
              class="btn btn-xs btn-ghost"
            >
              {{ expandedLogs.has(task.platform) ? '收起' : '展开' }}
            </button>
          </div>
          <div v-if="expandedLogs.has(task.platform)" class="logs-content">
            <div 
              v-for="(log, index) in task.logs.slice(-5)" 
              :key="index"
              class="log-line"
            >
              {{ log }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import {
  XMarkIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon,
  FolderIcon,
  CogIcon
} from '@heroicons/vue/24/outline';

import {useBuildStore, BuildStatus, BuildTask} from '../../stores/build';
import { NotificationService } from '../../../shared/services/NotificationService';

// Props
interface Props {
  showRecentTasks?: boolean;
  showLogs?: boolean;
  maxRecentTasks?: number;
}

const props = withDefaults(defineProps<Props>(), {
  showRecentTasks: true,
  showLogs: false,
  maxRecentTasks: 3
});

// Store
const buildStore = useBuildStore();

// 响应式数据
const expandedLogs = ref<Set<string>>(new Set());
const elapsedTimeInterval = ref<NodeJS.Timeout | null>(null);

// 计算属性
const displayTasks = computed(() => {
  const activeTasks = buildStore.activeTasks;
  
  if (!props.showRecentTasks) {
    return activeTasks;
  }
  
  // 显示活跃任务 + 最近完成的任务
  const recentTasks = buildStore.buildHistory
    .filter((task: BuildTask) => task.status !== BuildStatus.BUILDING)
    .slice(0, props.maxRecentTasks);
  
  return [...activeTasks, ...recentTasks];
});

// 生命周期
onMounted(() => {
  // 每秒更新经过时间
  elapsedTimeInterval.value = setInterval(() => {
    // 触发响应式更新
  }, 1000);
});

onUnmounted(() => {
  if (elapsedTimeInterval.value) {
    clearInterval(elapsedTimeInterval.value);
  }
});

// 方法
const cancelBuild = async (platform: string) => {
  try {
    await buildStore.cancelBuild(platform);
  } catch (error) {
    console.error('Failed to cancel build:', error);
  }
};

const cancelAllBuilds = async () => {
  const confirmed = confirm('确定要取消所有正在进行的构建吗？');
  if (!confirmed) return;

  const activeTasks = buildStore.activeTasks;
  for (const task of activeTasks) {
    try {
      await buildStore.cancelBuild(task.platform);
    } catch (error) {
      console.error(`Failed to cancel build for ${task.platform}:`, error);
    }
  }
};

const openBuildPath = (buildPath: string) => {
  if (window.electronAPI) {
    window.electronAPI.invoke('system:open-path', buildPath);
  }
};

const toggleLogs = (platform: string) => {
  if (expandedLogs.value.has(platform)) {
    expandedLogs.value.delete(platform);
  } else {
    expandedLogs.value.add(platform);
  }
};

const formatElapsedTime = (startTime: Date): string => {
  const elapsed = Date.now() - startTime.getTime();
  const seconds = Math.floor(elapsed / 1000);
  const minutes = Math.floor(seconds / 60);
  
  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  } else {
    return `${seconds}秒`;
  }
};

const getTaskItemClass = (task: any): string => {
  const baseClass = 'task-item';
  const statusClass = `task-${task.status}`;
  return `${baseClass} ${statusClass}`;
};

const getTaskIcon = (task: any) => {
  switch (task.status) {
    case BuildStatus.BUILDING:
      return CogIcon;
    case BuildStatus.SUCCESS:
      return CheckCircleIcon;
    case BuildStatus.FAILED:
      return ExclamationCircleIcon;
    case BuildStatus.CANCELLED:
      return XMarkIcon;
    default:
      return ClockIcon;
  }
};

const getTaskIconClass = (task: any): string => {
  switch (task.status) {
    case BuildStatus.BUILDING:
      return 'text-blue-500 animate-spin';
    case BuildStatus.SUCCESS:
      return 'text-green-500';
    case BuildStatus.FAILED:
      return 'text-red-500';
    case BuildStatus.CANCELLED:
      return 'text-orange-500';
    default:
      return 'text-gray-500';
  }
};
</script>

<style scoped>
.build-progress-panel {
  @apply bg-white border border-gray-200 rounded-lg p-4 space-y-4 mb-6;
  min-height: auto;
  overflow: visible;
}

.global-progress {
  @apply space-y-3;
}

.progress-header {
  @apply flex items-center justify-between;
}

.progress-info {
  @apply flex items-center space-x-2;
}

.progress-title {
  @apply text-sm font-medium text-gray-900;
}

.progress-actions {
  @apply flex space-x-2;
}

.global-progress-bar {
  @apply flex items-center space-x-3;
}

.progress-bar {
  @apply flex-1 bg-gray-200 rounded-full h-2;
}

.progress-fill {
  @apply bg-blue-500 h-2 rounded-full transition-all duration-300;
}

.progress-text {
  @apply text-sm font-medium text-gray-700 min-w-12 text-right;
}

.platform-tasks {
  @apply space-y-3;
}

.task-item {
  @apply bg-gray-50 border border-gray-200 rounded-lg p-3 space-y-2;
}

.task-item.task-building {
  @apply border-blue-200 bg-blue-50;
}

.task-item.task-success {
  @apply border-green-200 bg-green-50;
}

.task-item.task-failed {
  @apply border-red-200 bg-red-50;
}

.task-item.task-cancelled {
  @apply border-orange-200 bg-orange-50;
}

.task-header {
  @apply flex items-center justify-between;
}

.task-info {
  @apply flex items-center space-x-2;
}

.task-platform {
  @apply text-sm font-medium text-gray-900;
}

.task-status {
  @apply text-xs px-2 py-1 bg-gray-200 text-gray-700 rounded-full;
}

.task-actions {
  @apply flex items-center space-x-2;
}

.task-duration {
  @apply text-xs text-gray-500;
}

.task-progress {
  @apply flex items-center space-x-3;
}

.task-error {
  @apply flex items-center text-sm text-red-600;
}

.error-text {
  @apply truncate;
}

.task-result {
  @apply flex items-center text-sm text-green-600;
}

.result-text {
  @apply truncate;
}

.task-logs {
  @apply space-y-2;
}

.logs-header {
  @apply flex items-center justify-between;
}

.logs-title {
  @apply text-xs font-medium text-gray-700;
}

.logs-content {
  @apply bg-gray-900 text-green-400 text-xs p-2 rounded font-mono max-h-32 overflow-y-auto;
}

.log-line {
  @apply whitespace-pre-wrap break-all;
}
</style>
