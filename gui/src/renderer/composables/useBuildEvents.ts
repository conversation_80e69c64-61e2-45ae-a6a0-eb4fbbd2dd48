/**
 * 构建事件管理 Composable
 * 正确管理事件监听器的生命周期，避免内存泄漏
 */

import { ref, onUnmounted } from 'vue';
import { rendererEventBus } from '@shared/events/RendererEventBus';
import type { 
  BuildProgressEvent, 
  BuildCompleteEvent 
} from '../../main/events/MainEventBus';

export interface UseBuildEventsOptions {
  buildId?: string;
  onProgress?: (data: BuildProgressEvent) => void;
  onComplete?: (data: BuildCompleteEvent) => void;
}

/**
 * 构建事件监听 Composable
 * 在 Vue 组件中正确管理事件监听器
 */
export function useBuildEvents(options: UseBuildEventsOptions = {}) {
  const { buildId, onProgress, onComplete } = options;
  
  // 存储取消订阅函数
  const unsubscribers = ref<Array<() => void>>([]);
  
  // 是否已经设置监听器
  const isListening = ref(false);

  /**
   * 开始监听构建事件
   */
  const startListening = (targetBuildId?: string) => {
    if (isListening.value) {
      console.warn('⚠️ [useBuildEvents] Already listening, call stopListening() first');
      return;
    }

    const actualBuildId = targetBuildId || buildId;
    console.log(`🎧 [useBuildEvents] Starting to listen for buildId: ${actualBuildId}`);

    // 监听构建进度
    if (onProgress) {
      const progressUnsubscriber = rendererEventBus.subscribeBuildProgress((data: BuildProgressEvent) => {
        // 如果指定了 buildId，只处理匹配的事件
        if (actualBuildId && data.buildId !== actualBuildId) {
          return;
        }
        onProgress(data);
      });
      unsubscribers.value.push(progressUnsubscriber);
    }

    // 监听构建完成
    if (onComplete) {
      const completeUnsubscriber = rendererEventBus.subscribeBuildComplete((data: BuildCompleteEvent) => {
        // 如果指定了 buildId，只处理匹配的事件
        if (actualBuildId && data.buildId !== actualBuildId) {
          return;
        }
        onComplete(data);
      });
      unsubscribers.value.push(completeUnsubscriber);
    }

    isListening.value = true;
    console.log(`✅ [useBuildEvents] Started listening with ${unsubscribers.value.length} subscribers`);
  };

  /**
   * 停止监听构建事件
   */
  const stopListening = () => {
    if (!isListening.value) {
      return;
    }

    console.log(`🔇 [useBuildEvents] Stopping listening, cleaning up ${unsubscribers.value.length} subscribers`);
    
    unsubscribers.value.forEach(unsubscriber => {
      try {
        unsubscriber();
      } catch (error) {
        console.warn('⚠️ [useBuildEvents] Error cleaning up event listener:', error);
      }
    });
    
    unsubscribers.value = [];
    isListening.value = false;
    
    console.log('✅ [useBuildEvents] Stopped listening');
  };

  /**
   * 重新开始监听（先停止再开始）
   */
  const restartListening = (targetBuildId?: string) => {
    stopListening();
    startListening(targetBuildId);
  };

  // 组件卸载时自动清理
  onUnmounted(() => {
    console.log('🧹 [useBuildEvents] Component unmounting, cleaning up event listeners');
    stopListening();
  });

  return {
    // 状态
    isListening: readonly(isListening),
    
    // 方法
    startListening,
    stopListening,
    restartListening
  };
}

/**
 * 简化版本：只监听特定构建ID的事件
 */
export function useBuildEventsByID(buildId: string) {
  const buildProgress = ref<BuildProgressEvent | null>(null);
  const buildComplete = ref<BuildCompleteEvent | null>(null);
  const isBuilding = ref(false);

  const { startListening, stopListening, isListening } = useBuildEvents({
    buildId,
    onProgress: (data) => {
      buildProgress.value = data;
      isBuilding.value = data.progress < 100;
    },
    onComplete: (data) => {
      buildComplete.value = data;
      isBuilding.value = false;
    }
  });

  return {
    // 状态
    buildProgress: readonly(buildProgress),
    buildComplete: readonly(buildComplete),
    isBuilding: readonly(isBuilding),
    isListening,
    
    // 方法
    startListening,
    stopListening
  };
}

/**
 * 全局构建事件监听（监听所有构建事件）
 */
export function useGlobalBuildEvents() {
  const allBuildProgress = ref<Map<string, BuildProgressEvent>>(new Map());
  const allBuildComplete = ref<Map<string, BuildCompleteEvent>>(new Map());
  const activeBuildIds = ref<Set<string>>(new Set());

  const { startListening, stopListening, isListening } = useBuildEvents({
    onProgress: (data) => {
      allBuildProgress.value.set(data.buildId, data);
      if (data.progress < 100) {
        activeBuildIds.value.add(data.buildId);
      }
    },
    onComplete: (data) => {
      allBuildComplete.value.set(data.buildId, data);
      activeBuildIds.value.delete(data.buildId);
    }
  });

  // 获取特定构建的进度
  const getBuildProgress = (buildId: string) => {
    return allBuildProgress.value.get(buildId);
  };

  // 获取特定构建的完成状态
  const getBuildComplete = (buildId: string) => {
    return allBuildComplete.value.get(buildId);
  };

  // 检查特定构建是否正在进行
  const isBuildActive = (buildId: string) => {
    return activeBuildIds.value.has(buildId);
  };

  // 清理历史记录
  const clearHistory = () => {
    allBuildProgress.value.clear();
    allBuildComplete.value.clear();
    activeBuildIds.value.clear();
  };

  return {
    // 状态
    allBuildProgress: readonly(allBuildProgress),
    allBuildComplete: readonly(allBuildComplete),
    activeBuildIds: readonly(activeBuildIds),
    isListening,
    
    // 方法
    startListening,
    stopListening,
    getBuildProgress,
    getBuildComplete,
    isBuildActive,
    clearHistory
  };
}

// 导入 readonly 函数
import { readonly } from 'vue';
