/**
 * 渲染进程事件总线
 * 统一管理来自主进程的事件，简化 Store 中的事件监听逻辑
 * 复用现有的 IPC_COMMANDS.EVENTS 常量和 api.ts 类型定义
 */

import { ref, type Ref } from 'vue';
import { IPC_COMMANDS, EVENT_NAMES } from '../../shared/constants/ipc-commands';
import { ErrorHandler } from '../../shared/services/ErrorHandler';
import type {
  BuildProgressEvent,
  BuildCompleteEvent,
  VersionChangedEvent,
  DeployProgressEvent,
  DeployCompleteEvent
} from '../../main/events/MainEventBus';

// ==================== 事件监听器类型定义 ====================

export type EventCallback<T = any> = (data: T) => void;
export type EventUnsubscriber = () => void;

export interface EventSubscription {
  eventName: string;
  callback: EventCallback;
  unsubscribe: EventUnsubscriber;
}

// ==================== 渲染进程事件总线 ====================

class RendererEventBus {
  private static instance: RendererEventBus | null = null;
  private subscriptions = new Map<string, Set<EventCallback>>();
  private electronAPIAvailable: Ref<boolean> = ref(false);
  private eventStats = new Map<string, number>();
  private isInitialized = false;

  private constructor() {
    this.checkElectronAPI();
    this.setupEventListeners();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): RendererEventBus {
    if (!RendererEventBus.instance) {
      RendererEventBus.instance = new RendererEventBus();
    }
    return RendererEventBus.instance;
  }

  // ==================== 初始化和检查 ====================

  /**
   * 检查 Electron API 可用性
   */
  private checkElectronAPI(): void {
    const isAvailable = !!(window as any).electronAPI;
    this.electronAPIAvailable.value = isAvailable;
    
    if (!isAvailable) {
      console.warn('⚠️ [RendererEventBus] Electron API not available');
      ErrorHandler.handle(
        new Error('Electron API 不可用'),
        'RendererEventBus.checkElectronAPI',
        { showNotification: false, logError: true }
      );
    } else {
      console.log('✅ [RendererEventBus] Electron API available');
    }
  }

  /**
   * 设置主进程事件监听器
   */
  private setupEventListeners(): void {
    if (!this.electronAPIAvailable.value) {
      console.warn('⚠️ [RendererEventBus] Cannot setup event listeners - Electron API not available');
      return;
    }

    const electronAPI = (window as any).electronAPI;

    try {
      // 监听构建进度事件
      electronAPI.on(EVENT_NAMES.BUILD_PROGRESS, (data: BuildProgressEvent) => {
        this.handleEvent(EVENT_NAMES.BUILD_PROGRESS, data);
      });

      // 监听构建完成事件
      electronAPI.on(EVENT_NAMES.BUILD_COMPLETE, (data: BuildCompleteEvent) => {
        this.handleEvent(EVENT_NAMES.BUILD_COMPLETE, data);
      });

      // 监听构建日志事件
      electronAPI.on(EVENT_NAMES.BUILD_LOG, (data: any) => {
        this.handleEvent(EVENT_NAMES.BUILD_LOG, data);
      });

      // 监听版本变更事件
      electronAPI.on(EVENT_NAMES.VERSION_CHANGED, (data: VersionChangedEvent) => {
        this.handleEvent(EVENT_NAMES.VERSION_CHANGED, data);
      });

      // 监听部署进度事件
      electronAPI.on(EVENT_NAMES.DEPLOY_PROGRESS, (data: DeployProgressEvent) => {
        this.handleEvent(EVENT_NAMES.DEPLOY_PROGRESS, data);
      });

      // 监听部署完成事件
      electronAPI.on(EVENT_NAMES.DEPLOY_COMPLETE, (data: DeployCompleteEvent) => {
        this.handleEvent(EVENT_NAMES.DEPLOY_COMPLETE, data);
      });

      // 监听配置变更事件
      electronAPI.on(EVENT_NAMES.CONFIG_CHANGED, (data: any) => {
        this.handleEvent(EVENT_NAMES.CONFIG_CHANGED, data);
      });

      this.isInitialized = true;
      console.log('✅ [RendererEventBus] Event listeners setup complete');

    } catch (error) {
      console.error('❌ [RendererEventBus] Error setting up event listeners:', error);
      ErrorHandler.handle(
        error,
        'RendererEventBus.setupEventListeners',
        { showNotification: false, logError: true }
      );
    }
  }

  // ==================== 事件处理 ====================

  /**
   * 处理来自主进程的事件
   */
  private handleEvent(eventName: string, data: any): void {
    try {
      console.log(`📡 [RendererEventBus] Received event: ${eventName}`, data);

      // 更新事件统计
      const currentCount = this.eventStats.get(eventName) || 0;
      this.eventStats.set(eventName, currentCount + 1);

      // 分发给订阅者
      const callbacks = this.subscriptions.get(eventName);
      if (callbacks && callbacks.size > 0) {
        callbacks.forEach(callback => {
          try {
            callback(data);
          } catch (error) {
            console.error(`❌ [RendererEventBus] Error in event callback for ${eventName}:`, error);
            ErrorHandler.handle(
              error,
              `RendererEventBus.handleEvent.${eventName}`,
              { showNotification: false, logError: true }
            );
          }
        });
      } else {
        console.log(`📭 [RendererEventBus] No subscribers for event: ${eventName}`);
      }

    } catch (error) {
      console.error(`❌ [RendererEventBus] Error handling event ${eventName}:`, error);
      ErrorHandler.handle(
        error,
        'RendererEventBus.handleEvent',
        { showNotification: false, logError: true }
      );
    }
  }

  // ==================== 订阅管理 ====================

  /**
   * 订阅事件
   */
  subscribe<T = any>(eventName: string, callback: EventCallback<T>): EventUnsubscriber {
    if (!this.subscriptions.has(eventName)) {
      this.subscriptions.set(eventName, new Set());
    }

    const callbacks = this.subscriptions.get(eventName)!;
    callbacks.add(callback);

    console.log(`🎧 [RendererEventBus] Subscribed to event: ${eventName} (total subscribers: ${callbacks.size})`);

    // 返回取消订阅函数
    return () => {
      callbacks.delete(callback);
      console.log(`🔇 [RendererEventBus] Unsubscribed from event: ${eventName} (remaining subscribers: ${callbacks.size})`);
      
      // 如果没有订阅者了，清理 Set
      if (callbacks.size === 0) {
        this.subscriptions.delete(eventName);
      }
    };
  }

  /**
   * 订阅构建进度事件
   */
  subscribeBuildProgress(callback: EventCallback<BuildProgressEvent>): EventUnsubscriber {
    return this.subscribe(EVENT_NAMES.BUILD_PROGRESS, callback);
  }

  /**
   * 订阅构建完成事件
   */
  subscribeBuildComplete(callback: EventCallback<BuildCompleteEvent>): EventUnsubscriber {
    return this.subscribe(EVENT_NAMES.BUILD_COMPLETE, callback);
  }

  /**
   * 订阅版本变更事件
   */
  subscribeVersionChanged(callback: EventCallback<VersionChangedEvent>): EventUnsubscriber {
    return this.subscribe(EVENT_NAMES.VERSION_CHANGED, callback);
  }

  /**
   * 订阅部署进度事件
   */
  subscribeDeployProgress(callback: EventCallback<DeployProgressEvent>): EventUnsubscriber {
    return this.subscribe(EVENT_NAMES.DEPLOY_PROGRESS, callback);
  }

  /**
   * 订阅部署完成事件
   */
  subscribeDeployComplete(callback: EventCallback<DeployCompleteEvent>): EventUnsubscriber {
    return this.subscribe(EVENT_NAMES.DEPLOY_COMPLETE, callback);
  }

  // ==================== 工具方法 ====================

  /**
   * 获取 Electron API 可用性状态
   */
  get isElectronAPIAvailable(): boolean {
    return this.electronAPIAvailable.value;
  }

  /**
   * 获取初始化状态
   */
  get initialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 获取事件统计
   */
  getEventStats(): Map<string, number> {
    return new Map(this.eventStats);
  }

  /**
   * 获取订阅统计
   */
  getSubscriptionStats(): Map<string, number> {
    const stats = new Map<string, number>();
    this.subscriptions.forEach((callbacks, eventName) => {
      stats.set(eventName, callbacks.size);
    });
    return stats;
  }

  /**
   * 清除所有订阅
   */
  clearAllSubscriptions(): void {
    this.subscriptions.clear();
    console.log('🧹 [RendererEventBus] All subscriptions cleared');
  }

  /**
   * 销毁事件总线
   */
  destroy(): void {
    this.clearAllSubscriptions();
    this.eventStats.clear();
    this.isInitialized = false;
    RendererEventBus.instance = null;
    console.log('💥 [RendererEventBus] Event bus destroyed');
  }
}

// 导出单例实例
export const rendererEventBus = RendererEventBus.getInstance();
export default rendererEventBus;
