/**
 * 构建状态管理 Store
 * 管理全局构建状态，支持多平台独立构建状态跟踪
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { apiClient } from '@shared/api/VersionCraftAPIClient.ts';
import { NotificationService } from '@shared/services/NotificationService.ts';
import { ErrorHandler } from '@shared/services/ErrorHandler.ts';
import { IPC_COMMANDS } from '@shared/constants/ipc-commands.ts';
import { rendererEventBus } from '@shared/events/RendererEventBus';
import type {
  BuildProgressEvent,
  BuildCompleteEvent
} from '../../main/events/MainEventBus';

// 构建任务状态枚举
export enum BuildStatus {
  IDLE = 'idle',           // 空闲
  PREPARING = 'preparing', // 准备构建
  BUILDING = 'building',   // 构建中
  SUCCESS = 'success',     // 构建成功
  FAILED = 'failed',       // 构建失败
  CANCELLED = 'cancelled'  // 已取消
}

// 构建任务接口
export interface BuildTask {
  id: string;
  platform: string;
  status: BuildStatus;
  progress: number;
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  error?: string;
  logs?: string[];
  buildPath?: string;
  // 兼容模板中的属性
  type?: string;
  statusText?: string;
  version?: string;
  success?: boolean;
  timestamp?: string;
  size?: number;
  artifactPath?: string;
}

// 构建统计接口
export interface BuildStats {
  totalBuilds: number;
  successfulBuilds: number;
  failedBuilds: number;
  averageBuildTime: number;
  lastBuildTime?: Date;
}

export const useBuildStore = defineStore('build', () => {
  // 状态 - 使用 reactive 对象而不是 Map 来确保响应式
  const buildTasks = ref<Record<string, BuildTask>>({});
  const buildHistory = ref<BuildTask[]>([]);
  const buildStats = ref<BuildStats>({
    totalBuilds: 0,
    successfulBuilds: 0,
    failedBuilds: 0,
    averageBuildTime: 0,
  });

  // 计算属性
  const activeTasks = computed(() => {
    return Object.values(buildTasks.value).filter(
      task => task.status === BuildStatus.BUILDING || task.status === BuildStatus.PREPARING
    );
  });

  const isAnyBuilding = computed(() => {
    return activeTasks.value.length > 0;
  });

  const isPlatformBuilding = computed(() => {
    return (platform: string) => {
      const task = buildTasks.value[platform];
      return task?.status === BuildStatus.BUILDING || task?.status === BuildStatus.PREPARING;
    };
  });

  const getPlatformTask = computed(() => {
    return (platform: string) => {
      return buildTasks.value[platform];
    };
  });

  const buildProgress = computed(() => {
    const tasks = activeTasks.value;
    if (tasks.length === 0) return 0;

    const totalProgress = tasks.reduce((sum, task) => sum + task.progress, 0);
    return Math.round(totalProgress / tasks.length);
  });

  const getPlatformName = (platform: string) => {
    const platformNames: Record<string, string> = {
      'web-mobile': 'Web 移动端',
      'android': 'Android',
      'ios': 'iOS'
    };
    return platformNames[platform] || platform;
  };

  // 方法
  const startBuild = async (platform: string, options: any = {}): Promise<string> => {
    try {
      // 严格检查：同一平台只能有一次构建
      const existingTask = buildTasks.value[platform];
      if (existingTask && existingTask.status === BuildStatus.BUILDING) {
        throw new Error(`${getPlatformName(platform)} 平台正在构建中，请等待完成后再试`);
      }

      // 双重检查：确保没有遗漏的构建状态
      if (isPlatformBuilding.value(platform)) {
        throw new Error(`${getPlatformName(platform)} 平台构建状态异常，请刷新页面后重试`);
      }

      // 检查并发构建限制（最多同时1个构建任务，确保系统稳定）
      if (activeTasks.value.length >= 1) {
        const runningPlatforms = activeTasks.value.map(task => getPlatformName(task.platform)).join('、');
        throw new Error(`当前 ${runningPlatforms} 正在构建中，请等待完成后再试`);
      }

      // 防抖检查：避免短时间内重复启动
      const lastStartTime = localStorage.getItem(`build_start_${platform}`);
      const now = Date.now();
      if (lastStartTime && (now - parseInt(lastStartTime)) < 3000) {
        throw new Error('请勿频繁启动构建任务，请等待3秒后重试');
      }
      localStorage.setItem(`build_start_${platform}`, now.toString());

      // 立即创建准备状态的构建任务
      const taskId = `${platform}-${Date.now()}`;
      const preparingTask: BuildTask = {
        id: taskId,
        platform,
        status: BuildStatus.PREPARING,
        progress: 0,
        startTime: new Date(),
        logs: []
      };

      buildTasks.value[platform] = preparingTask;
      console.log(`🔄 [BuildStore] Preparing build for ${platform}:`, preparingTask);

      // 调用主进程开始构建
      if (!window.electronAPI) {
        // 如果API不可用，移除准备状态
        delete buildTasks.value[platform];
        throw new Error('Electron API 不可用，无法启动构建');
      }

      const result = await window.electronAPI.invoke(IPC_COMMANDS.BUILD.START, platform, options);

      if (!result.success) {
        // 清除防抖记录和准备状态，允许重试
        localStorage.removeItem(`build_start_${platform}`);
        delete buildTasks.value[platform];
        throw new Error(result.error || '启动构建失败');
      }

      // 更新任务状态为构建中
      const actualTaskId = result.data.buildId || taskId;
      const buildingTask: BuildTask = {
        id: actualTaskId,
        platform,
        status: BuildStatus.BUILDING,
        progress: 0,
        startTime: new Date(),
        logs: []
      };

      buildTasks.value[platform] = buildingTask;

      // 设置事件监听器
      setupBuildEventListeners(actualTaskId, platform);

      console.log(`🚀 [BuildStore] Started build for ${platform}:`, buildingTask);
      return actualTaskId;
    } catch (error) {
      console.error(`❌ [BuildStore] Failed to start build for ${platform}:`, error);
      throw error;
    }
  };

  const updateBuildProgress = (platform: string, progress: number, logs?: string[]) => {
    const task = buildTasks.value[platform];
    if (task && task.status === BuildStatus.BUILDING) {
      task.progress = Math.min(100, Math.max(0, progress));
      if (logs) {
        task.logs = [...(task.logs || []), ...logs];
      }

      console.log(`📊 [BuildStore] Updated ${platform} progress: ${progress}%`);
    }
  };

  const completeBuild = (platform: string, success: boolean, error?: string, buildPath?: string) => {
    const task = buildTasks.value[platform];
    if (task) {
      task.status = success ? BuildStatus.SUCCESS : BuildStatus.FAILED;
      task.progress = success ? 100 : task.progress;
      task.endTime = new Date();
      task.error = error;
      task.buildPath = buildPath;
      
      if (task.startTime && task.endTime) {
        task.duration = task.endTime.getTime() - task.startTime.getTime();
      }

      // 添加到历史记录
      buildHistory.value.unshift({ ...task });
      
      // 更新统计
      updateStats(task);
      
      // 通知用户
      if (success) {
        NotificationService.success(`${platform} 构建完成`);
      } else {
        NotificationService.error(`${platform} 构建失败: ${error || '未知错误'}`);
      }
      
      console.log(`✅ [BuildStore] Completed build for ${platform}:`, task);
      
      // 延迟清理任务状态（保持一段时间显示结果）
      setTimeout(() => {
        if (buildTasks.value[platform]?.id === task.id) {
          delete buildTasks.value[platform];
        }
      }, 3000);
    }
  };

  const cancelBuild = async (platform: string) => {
    const task = buildTasks.value[platform];
    if (!task || task.status !== BuildStatus.BUILDING) {
      return;
    }

    try {
      // 调用主进程取消构建
      if (window.electronAPI) {
        const result = await window.electronAPI.invoke(IPC_COMMANDS.BUILD.CANCEL, task.id);
        if (!result.success) {
          console.warn(`Failed to cancel build via API: ${result.error}`);
        }
      }

      // 更新任务状态
      task.status = BuildStatus.CANCELLED;
      task.endTime = new Date();

      if (task.startTime && task.endTime) {
        task.duration = task.endTime.getTime() - task.startTime.getTime();
      }

      // 添加到历史记录
      buildHistory.value.unshift({ ...task });
      updateStats(task);

      console.log(`❌ [BuildStore] Cancelled build for ${platform}`);
      NotificationService.info(`${getPlatformName(platform)} 构建已取消`);

      // 延迟清理
      setTimeout(() => {
        delete buildTasks.value[platform];
      }, 1000);
    } catch (error) {
      console.error(`Failed to cancel build for ${platform}:`, error);
    }
  };

  const clearTask = (platform: string) => {
    delete buildTasks.value[platform];
    console.log(`🧹 [BuildStore] Cleared task for ${platform}`);
  };

  const clearAllTasks = () => {
    buildTasks.value = {};
    console.log(`🧹 [BuildStore] Cleared all tasks`);
  };

  // 事件监听器取消订阅函数存储
  const eventUnsubscribers = ref<Array<() => void>>([]);

  // 设置构建事件监听器 - 使用事件总线优化
  const setupBuildEventListeners = (buildId: string, platform: string) => {
    console.log(`🎧 [BuildStore] Setting up EventBus listeners for buildId: ${buildId}, platform: ${platform}`);
    console.log('🔍 [BuildStore] DEBUG - rendererEventBus available:', rendererEventBus.initialized);

    // 清理之前的监听器
    cleanupEventListeners();

    // 使用事件总线监听构建进度
    const progressUnsubscriber = rendererEventBus.subscribeBuildProgress((data: BuildProgressEvent) => {
      console.log('🎯 [BuildStore] Received BUILD_PROGRESS event via EventBus:', data);
      console.log('🎯 [BuildStore] Expected buildId:', buildId, 'Received buildId:', data.buildId);

      if (data.buildId === buildId) {
        console.log('✅ [BuildStore] BuildId matches, updating progress:', data.progress);
        updateBuildProgress(platform, data.progress || 0, data.message ? [data.message] : undefined);
      } else {
        console.log('❌ [BuildStore] BuildId mismatch, ignoring event');
      }
    });

    // 使用事件总线监听构建完成
    const completeUnsubscriber = rendererEventBus.subscribeBuildComplete((data: BuildCompleteEvent) => {
      console.log('🎯 [BuildStore] Received BUILD_COMPLETE event via EventBus:', data);
      if (data.buildId === buildId) {
        completeBuild(platform, data.success, data.error);
      }
    });

    // 存储取消订阅函数
    eventUnsubscribers.value.push(progressUnsubscriber, completeUnsubscriber);

    // 返回清理函数，供组件使用
    return () => {
      progressUnsubscriber();
      completeUnsubscriber();
    };

    // 监听构建日志事件（保持 IPC 方式，因为事件总线暂未实现日志事件）
    if (window.electronAPI) {
      window.electronAPI.on(IPC_COMMANDS.EVENTS.BUILD_LOG, (data: any) => {
        if (data.buildId === buildId) {
          const task = buildTasks.value[platform];
          if (task) {
            const timestamp = new Date().toLocaleTimeString();
            const level = data.level || 'INFO';
            const logMessage = `[${timestamp}] [${level}] ${data.message}`;
            task.logs = [...(task.logs || []), logMessage];
          }
        }
      });
    }

    console.log('✅ [BuildStore] EventBus listeners setup complete');
  };

  // 清理事件监听器
  const cleanupEventListeners = () => {
    eventUnsubscribers.value.forEach(unsubscriber => {
      try {
        unsubscriber();
      } catch (error) {
        console.warn('⚠️ [BuildStore] Error cleaning up event listener:', error);
      }
    });
    eventUnsubscribers.value = [];
    console.log('🧹 [BuildStore] Event listeners cleaned up');
  };

  const updateStats = (task: BuildTask) => {
    buildStats.value.totalBuilds++;

    if (task.status === BuildStatus.SUCCESS) {
      buildStats.value.successfulBuilds++;
    } else if (task.status === BuildStatus.FAILED) {
      buildStats.value.failedBuilds++;
    }

    if (task.duration) {
      const totalDuration = buildStats.value.averageBuildTime * (buildStats.value.totalBuilds - 1) + task.duration;
      buildStats.value.averageBuildTime = Math.round(totalDuration / buildStats.value.totalBuilds);
    }

    buildStats.value.lastBuildTime = task.endTime;
  };

  const loadBuildHistory = async () => {
    try {
      const result = await apiClient.build.getHistory();
      if (result.success && result.data) {
        buildHistory.value = result.data.map((item: any) => ({
          id: item.id,
          platform: item.platform,
          status: item.success ? BuildStatus.SUCCESS : BuildStatus.FAILED,
          progress: 100,
          startTime: new Date(item.startTime),
          endTime: new Date(item.endTime),
          duration: item.duration,
          error: item.error,
          buildPath: item.buildPath
        }));
      }
    } catch (error) {
      console.error('Failed to load build history:', error);
    }
  };

  const loadBuildStats = async () => {
    try {
      const result = await apiClient.build.getStats();
      if (result.success && result.data) {
        buildStats.value = {
          totalBuilds: result.data.totalBuilds || 0,
          successfulBuilds: result.data.successfulBuilds || 0,
          failedBuilds: result.data.failedBuilds || 0,
          averageBuildTime: result.data.averageBuildTime || 0,
          lastBuildTime: result.data.lastBuildTime ? new Date(result.data.lastBuildTime) : undefined
        };
      }
    } catch (error) {
      console.error('Failed to load build stats:', error);
    }
  };

  // 格式化持续时间
  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: BuildStatus): string => {
    const colors: Record<BuildStatus, string> = {
      [BuildStatus.IDLE]: 'gray',
      [BuildStatus.PREPARING]: 'yellow',
      [BuildStatus.BUILDING]: 'blue',
      [BuildStatus.SUCCESS]: 'green',
      [BuildStatus.FAILED]: 'red',
      [BuildStatus.CANCELLED]: 'orange'
    };
    return colors[status];
  };

  // 获取状态文本
  const getStatusText = (status: BuildStatus): string => {
    const texts: Record<BuildStatus, string> = {
      [BuildStatus.IDLE]: '空闲',
      [BuildStatus.PREPARING]: '准备中',
      [BuildStatus.BUILDING]: '构建中',
      [BuildStatus.SUCCESS]: '成功',
      [BuildStatus.FAILED]: '失败',
      [BuildStatus.CANCELLED]: '已取消'
    };
    return texts[status];
  };

  return {
    // 状态
    buildTasks,
    buildHistory,
    buildStats,
    
    // 计算属性
    activeTasks,
    isAnyBuilding,
    isPlatformBuilding,
    getPlatformTask,
    buildProgress,
    
    // 方法
    startBuild,
    updateBuildProgress,
    completeBuild,
    cancelBuild,
    clearTask,
    clearAllTasks,
    loadBuildHistory,
    loadBuildStats,

    // 事件管理
    cleanupEventListeners,

    // 工具方法
    getPlatformName,
    formatDuration,
    getStatusColor,
    getStatusText
  };
});
