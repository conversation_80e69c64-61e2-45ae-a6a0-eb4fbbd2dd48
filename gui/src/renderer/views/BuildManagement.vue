<!--
  构建管理页面
  对等 CLI 所有构建功能的完整实现
-->

<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">构建管理</h1>
        <p class="text-gray-600 mt-1">管理多平台构建任务，监控构建状态和历史记录</p>
      </div>
      <div class="flex space-x-3">
        <button
          @click="refreshData"
          :disabled="isLoading"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <ArrowPathIcon class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" />
          刷新
        </button>
        <button
          @click="showBuildModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PlayIcon class="w-4 h-4 mr-2" />
          高级构建
        </button>
        <button
          @click="showBuildHistoryModal = true"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <ClockIcon class="w-4 h-4 mr-2" />
          构建历史
        </button>
        <button
          @click="showQueueManager = !showQueueManager"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          :class="{ 'bg-blue-50 border-blue-300 text-blue-700': showQueueManager }"
        >
          <QueueListIcon class="w-4 h-4 mr-2" />
          {{ showQueueManager ? '隐藏队列' : '队列管理' }}
        </button>
        <button
          @click="showBuildConfigModal = true"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <CogIcon class="w-4 h-4 mr-2" />
          构建配置
        </button>
      </div>
    </div>

    <!-- 构建进度面板 - 动态显示 -->
    <div v-if="buildStore.activeTasks.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <BuildProgressPanel
        :show-recent-tasks="true"
        :show-logs="false"
        :max-recent-tasks="3"
      />
    </div>

    <!-- 构建队列管理器 -->
    <div v-if="showQueueManager" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <BuildQueueManager
        ref="queueManager"
      />
    </div>

    <!-- 快速构建 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">快速构建</h2>
        <div class="flex items-center space-x-3">
          <VCSelect
            v-model="quickBuildType"
            :options="buildTypeOptions"
            placeholder="构建类型"
            class="w-48"
          />
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="platform in platforms"
          :key="platform.id"
          class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          :class="{ 'border-blue-300 bg-blue-50': buildStore.isPlatformBuilding(platform.id) }"
        >
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <component :is="getPlatformIcon(platform.id)" class="w-8 h-8 text-gray-600" />
              </div>
              <div>
                <h3 class="text-lg font-medium text-gray-900">{{ platform.name }}</h3>
                <p class="text-sm text-gray-500">{{ platform.description }}</p>
              </div>
            </div>
            <div class="flex-shrink-0">
              <BuildStatusIndicator
                :platform="platform.id"
                size="sm"
                :show-progress="false"
                :show-duration="false"
                :show-actions="false"
              />
            </div>
          </div>

          <div class="space-y-2 mb-4">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500">上次构建:</span>
              <span class="text-gray-900">{{ getLastBuildTime(platform.id) }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500">构建次数:</span>
              <span class="text-gray-900">{{ getBuildCount(platform.id) }}</span>
            </div>
          </div>

          <div class="flex space-x-2">
            <button
              @click="quickBuild(platform.id)"
              :disabled="buildStore.isPlatformBuilding(platform.id) || buildStore.isAnyBuilding"
              class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              :title="getButtonDisabledReason(platform.id)"
            >
              <PlayIcon v-if="!buildStore.isPlatformBuilding(platform.id)" class="w-4 h-4 mr-1" />
              <div v-else class="w-4 h-4 mr-1 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              {{ getPlatformButtonText(platform.id) }}
            </button>
            <button
              @click="showPlatformConfig(platform.id)"
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <CogIcon class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 构建统计 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">构建统计</h2>
        <div class="flex items-center space-x-3">
          <VCSelect
            v-model="statsTimeRange"
            :options="timeRangeOptions"
            placeholder="时间范围"
            class="w-48"
          />
        </div>
      </div>
      
      <div v-if="isLoadingStats" class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="ml-3 text-gray-600">加载统计信息...</p>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gray-50 rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <PlayIcon class="w-8 h-8 text-blue-500" />
            </div>
            <div class="ml-4">
              <p class="text-2xl font-semibold text-gray-900">{{ buildStore.buildStats.totalBuilds || 0 }}</p>
              <p class="text-sm text-gray-600">总构建次数</p>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CheckCircleIcon class="w-8 h-8 text-green-500" />
            </div>
            <div class="ml-4">
              <p class="text-2xl font-semibold text-gray-900">{{ buildStore.buildStats.successfulBuilds || 0 }}</p>
              <p class="text-sm text-gray-600">成功构建</p>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <XCircleIcon class="w-8 h-8 text-red-500" />
            </div>
            <div class="ml-4">
              <p class="text-2xl font-semibold text-gray-900">{{ buildStore.buildStats.failedBuilds || 0 }}</p>
              <p class="text-sm text-gray-600">失败构建</p>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <ClockIcon class="w-8 h-8 text-purple-500" />
            </div>
            <div class="ml-4">
              <p class="text-2xl font-semibold text-gray-900">{{ buildStore.formatDuration(buildStore.buildStats.averageBuildTime || 0) }}</p>
              <p class="text-sm text-gray-600">平均耗时</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 构建历史 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">构建历史</h2>
        <div class="flex items-center space-x-3">
          <VCSelect
            v-model="historyFilter"
            :options="historyFilterOptions"
            placeholder="筛选记录"
            class="w-48"
          />
          <button
            @click="showBuildHistoryModal = true"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <EyeIcon class="w-4 h-4 mr-2" />
            查看全部
          </button>
        </div>
      </div>
      
      <div v-if="isLoadingHistory" class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="ml-3 text-gray-600">加载构建历史...</p>
      </div>

      <div v-else-if="filteredBuildHistory.length === 0" class="text-center py-12">
        <ClockIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p class="text-gray-500">暂无构建历史</p>
      </div>

      <div v-else class="space-y-4">
        <div
          v-for="(build, index) in filteredBuildHistory.slice(0, 10)"
          :key="build.id || index"
          class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <component :is="getBuildIcon(build)" class="w-5 h-5" :class="(build.success ?? (build.status === 'success')) ? 'text-green-500' : 'text-red-500'" />
              </div>
              <div class="flex items-center space-x-2">
                <component :is="getPlatformIcon(build.platform)" class="w-4 h-4 text-gray-600" />
                <span class="font-medium text-gray-900">{{ getPlatformName(build.platform) }}</span>
                <ArrowRightIcon class="w-4 h-4 text-gray-400" />
                <span class="text-sm text-gray-600">{{ build.version || '1.0.0' }}</span>
              </div>
            </div>
            <div class="flex-shrink-0">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="(build.success ?? (build.status === 'success')) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                {{ (build.success ?? (build.status === 'success')) ? '成功' : '失败' }}
              </span>
            </div>
          </div>
          
          <div class="flex items-center justify-between text-sm text-gray-500">
            <div class="flex items-center space-x-4">
              <span>{{ formatTime(build.timestamp || (build.endTime ? build.endTime.toISOString() : '')) }}</span>
              <span v-if="build.duration">耗时: {{ formatDuration(build.duration) }}</span>
              <span v-if="build.size">大小: {{ formatSize(build.size) }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="onViewBuildDetails(build)"
                class="text-blue-600 hover:text-blue-800"
              >
                查看详情
              </button>
            </div>
          </div>
          
          <div v-if="build.error" class="mt-2 flex items-center text-sm text-red-600">
            <ExclamationCircleIcon class="w-4 h-4 mr-1" />
            {{ build.error }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 模态框组件 -->
  <BuildConfigModal
    v-model="showBuildConfigModal"
    @save="() => onBuildConfigSaved({})"
  />

  <BuildModal
    v-model="showBuildModal"
    :platforms="platforms"
    @build="executeBuild"
  />

  <BuildHistoryModal
    v-model="showBuildHistoryModal"
    :build-history="buildStore.buildHistory"
    @view-details="onViewBuildDetails"
    @rebuild="onRebuildFromHistory"
  />

  <BuildDetailsModal
    v-model="showBuildDetailsModal"
    :build-record="selectedBuild"
    @rebuild="onRebuildFromDetails"
    @view-logs="onViewBuildLogs"
  />

  <BuildLogsModal
    v-model="showBuildLogsModal"
    :build-logs="selectedBuildLogs"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import {
  ArrowPathIcon,
  CogIcon,
  PlayIcon,
  EyeIcon,
  XMarkIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowRightIcon,
  ExclamationCircleIcon,
  ArrowDownTrayIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon,
  QueueListIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '@shared/api/VersionCraftAPIClient.ts';
import { ErrorHandler } from '@shared/services/ErrorHandler.ts';
import { NotificationService } from '@shared/services/NotificationService.ts';
import { useBuildStore, BuildStatus } from '../stores/build';

// 导入基础组件
import VCSelect from '../components/ui/VCSelect.vue';

// 导入构建组件
import BuildProgressPanel from '../components/build/BuildProgressPanel.vue';
import BuildStatusIndicator from '../components/build/BuildStatusIndicator.vue';
import BuildQueueManager from '../components/build/BuildQueueManager.vue';

// 导入模态框组件
import BuildConfigModal from '../components/modals/BuildConfigModal.vue';
import BuildModal from '../components/modals/BuildModal.vue';
import BuildHistoryModal from '../components/modals/BuildHistoryModal.vue';
import BuildDetailsModal from '../components/modals/BuildDetailsModal.vue';
import BuildLogsModal from '../components/modals/BuildLogsModal.vue';

// 响应式数据
const isLoading = ref(false);
const isLoadingStats = ref(false);
const isLoadingHistory = ref(false);
const quickBuildType = ref('debug');
const statsTimeRange = ref('week');
const historyFilter = ref('all');
const showQueueManager = ref(false);

// 组件引用
const queueManager = ref<InstanceType<typeof BuildQueueManager> | null>(null);

// 模态框状态
const showBuildModal = ref(false);
const showBuildConfigModal = ref(false);
const showBuildHistoryModal = ref(false);
const showBuildDetailsModal = ref(false);
const showBuildLogsModal = ref(false);

// 数据（使用 buildStore 中的数据）
const selectedBuild = ref<any>(null);
const selectedBuildLogs = ref<{
  buildId?: string;
  logs: Array<{
    timestamp: string;
    level: string;
    message: string;
    source?: string;
  }>;
} | undefined>(undefined);

// 轮询定时器
let buildPollingTimer: NodeJS.Timeout | null = null;

// 平台配置
const platforms = ref([
  {
    id: 'web-mobile',
    name: 'Web 移动端',
    description: '移动端 Web 应用',
    icon: ComputerDesktopIcon
  },
  {
    id: 'android',
    name: 'Android',
    description: 'Android 应用',
    icon: DevicePhoneMobileIcon
  },
  {
    id: 'ios',
    name: 'iOS',
    description: 'iOS 应用',
    icon: DeviceTabletIcon
  }
]);

// 计算属性
const buildTypeOptions = computed(() => [
  { label: '调试构建', value: 'debug' },
  { label: '发布构建', value: 'release' },
  { label: '测试构建', value: 'test' }
]);

const timeRangeOptions = computed(() => [
  { label: '最近一周', value: 'week' },
  { label: '最近一月', value: 'month' },
  { label: '最近三月', value: 'quarter' }
]);

const historyFilterOptions = computed(() => [
  { label: '全部记录', value: 'all' },
  { label: '成功构建', value: 'success' },
  { label: '失败构建', value: 'failed' },
  { label: 'Web 移动端', value: 'web-mobile' },
  { label: 'Android', value: 'android' },
  { label: 'iOS', value: 'ios' }
]);

const filteredBuildHistory = computed(() => {
  const history = buildStore.buildHistory || [];

  if (historyFilter.value === 'all') {
    return history;
  }

  return history.filter((build: any) => {
    switch (historyFilter.value) {
      case 'success':
        return build.success;
      case 'failed':
        return !build.success;
      case 'web-mobile':
      case 'android':
      case 'ios':
        return build.platform === historyFilter.value;
      default:
        return true;
    }
  });
});

// Store
const buildStore = useBuildStore();

// 生命周期
onMounted(async () => {
  await loadData();
  startBuildPolling();
});

onUnmounted(() => {
  stopBuildPolling();
});

// 方法
const loadData = async () => {
  await Promise.all([
    buildStore.loadBuildHistory(),
    buildStore.loadBuildStats()
  ]);
};

const refreshData = async () => {
  isLoading.value = true;
  await loadData();
  isLoading.value = false;
  NotificationService.success('数据已刷新');
};

const startBuildPolling = () => {
  // 每10秒轮询一次构建状态
  buildPollingTimer = setInterval(async () => {
    if (buildStore.isAnyBuilding) {
      // 构建状态现在通过事件监听器实时更新，无需轮询
      console.log('Build polling - active tasks:', buildStore.activeTasks.length);
    }
  }, 10000);
};

const stopBuildPolling = () => {
  if (buildPollingTimer) {
    clearInterval(buildPollingTimer);
    buildPollingTimer = null;
  }
};

// 构建相关方法
const quickBuild = async (platformId: string) => {
  try {
    // 检查是否已在构建
    if (buildStore.isPlatformBuilding(platformId)) {
      NotificationService.warning(`${buildStore.getPlatformName(platformId)} 正在构建中`);
      return;
    }

    // 检查是否有其他平台正在构建（防止并发构建导致系统卡死）
    if (buildStore.isAnyBuilding && buildStore.activeTasks.length >= 2) {
      NotificationService.warning('当前有多个构建任务正在进行，请稍后再试');
      return;
    }

    console.log(`Starting quick build for platform: ${platformId}, type: ${quickBuildType.value}`);

    // 开始构建
    await buildStore.startBuild(platformId, {
      buildType: quickBuildType.value,
      source: 'quick-build'
    });

    NotificationService.success(`${buildStore.getPlatformName(platformId)} 构建已开始`);

  } catch (error) {
    console.error('Quick build failed:', error);
    ErrorHandler.handle(error, '快速构建失败');
  }
};

const executeBuild = async (buildConfig: any) => {
  try {
    console.log('Executing advanced build with config:', buildConfig);

    // 开始构建
    await buildStore.startBuild(buildConfig.platform, {
      buildType: buildConfig.buildType,
      version: buildConfig.version,
      outputPath: buildConfig.outputPath,
      source: 'advanced-build',
      ...buildConfig.options
    });

    NotificationService.success(`${buildStore.getPlatformName(buildConfig.platform)} 高级构建已开始`);
    showBuildModal.value = false;

  } catch (error) {
    console.error('Advanced build failed:', error);
    ErrorHandler.handle(error, '高级构建失败');
  }
};

// 平台相关方法
const getPlatformIcon = (platformId: string) => {
  const platform = platforms.value.find(p => p.id === platformId);
  return platform?.icon || ComputerDesktopIcon;
};

const getPlatformName = (platformId: string) => {
  const platform = platforms.value.find(p => p.id === platformId);
  return platform?.name || platformId;
};

const getLastBuildTime = (platformId: string) => {
  const builds = buildStore.buildHistory?.filter((build: any) => build.platform === platformId) || [];
  if (builds.length === 0) return '从未构建';

  const lastBuild = builds[0];
  const timeValue = lastBuild.timestamp || lastBuild.endTime;
  if (!timeValue) return '时间未知';

  const time = new Date(timeValue);
  return time.toLocaleString('zh-CN');
};

const getBuildCount = (platformId: string) => {
  const builds = buildStore.buildHistory?.filter((build: any) => build.platform === platformId) || [];
  return builds.length;
};

const getPlatformButtonText = (platformId: string) => {
  if (buildStore.isPlatformBuilding(platformId)) {
    return '构建中...';
  }
  return '开始构建';
};

const getButtonDisabledReason = (platformId: string) => {
  if (buildStore.isPlatformBuilding(platformId)) {
    return '当前平台正在构建中';
  }
  if (buildStore.isAnyBuilding) {
    return '其他平台正在构建中，请等待完成';
  }
  return '';
};

const showPlatformConfig = (platformId: string) => {
  // 打开构建配置模态框，并切换到对应平台
  showBuildConfigModal.value = true;
  // 可以通过事件或其他方式通知模态框切换到指定平台
  NotificationService.info(`打开 ${getPlatformName(platformId)} 平台配置`);
};

// 构建历史相关方法
const getBuildIcon = (build: any) => {
  const success = build.success ?? (build.status === 'success');
  return success ? CheckCircleIcon : XCircleIcon;
};

const onViewBuildDetails = (build: any) => {
  selectedBuild.value = build;
  showBuildDetailsModal.value = true;
};

const onRebuildFromHistory = async (build: any) => {
  try {
    await buildStore.startBuild(build.platform, {
      buildType: build.buildType || 'debug',
      version: build.version,
      source: 'rebuild-from-history'
    });

    NotificationService.success(`${getPlatformName(build.platform)} 重新构建已开始`);
  } catch (error) {
    ErrorHandler.handle(error, '重新构建失败');
  }
};

const onRebuildFromDetails = async (build: any) => {
  try {
    await buildStore.startBuild(build.platform, {
      buildType: build.buildType || 'debug',
      version: build.version,
      source: 'rebuild-from-details'
    });

    NotificationService.success(`${getPlatformName(build.platform)} 重新构建已开始`);
    showBuildDetailsModal.value = false;
  } catch (error) {
    ErrorHandler.handle(error, '重新构建失败');
  }
};

const onViewBuildLogs = (build: any) => {
  selectedBuildLogs.value = {
    buildId: build.id,
    logs: build.logs || []
  };
  showBuildLogsModal.value = true;
};

const onBuildConfigSaved = (config: any) => {
  console.log('Build config saved:', config);
  NotificationService.success('构建配置已保存');
};

// 工具方法
const formatTime = (timeString: string) => {
  if (!timeString) return '-';

  const time = new Date(timeString);
  return time.toLocaleString('zh-CN');
};

const formatDuration = (ms: number) => {
  if (!ms) return '-';

  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);

  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  }
  return `${seconds}秒`;
};

const formatSize = (bytes: number) => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>
