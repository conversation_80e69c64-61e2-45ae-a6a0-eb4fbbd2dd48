<!--
  构建管理页面
  对等 CLI 所有构建功能的完整实现
-->

<template>
  <div class="build-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">构建管理</h1>
          <p class="page-subtitle">管理多平台构建任务，监控构建状态和历史记录</p>
        </div>
      </div>
      <div class="header-actions">
          <button
            @click="refreshData"
            :disabled="isLoading"
            class="btn btn-secondary"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" />
            刷新
          </button>
          <button
            @click="showBuildModal = true"
            class="btn btn-primary"
          >
            <PlayIcon class="w-4 h-4 mr-2" />
            高级构建
          </button>
          <button
            @click="showBuildHistoryModal = true"
            class="btn btn-secondary"
          >
            <ClockIcon class="w-4 h-4 mr-2" />
            构建历史
          </button>
          <button
            @click="showQueueManager = !showQueueManager"
            class="btn btn-secondary"
            :class="{ 'btn-active': showQueueManager }"
          >
            <QueueListIcon class="w-4 h-4 mr-2" />
            {{ showQueueManager ? '隐藏队列' : '队列管理' }}
          </button>
          <button
            @click="showBuildConfigModal = true"
            class="btn btn-secondary"
          >
            <CogIcon class="w-4 h-4 mr-2" />
            构建配置
          </button>
        </div>
    </div>

<!--     构建进度面板 - 动态显示
    <div v-if="buildStore.activeTasks.length > 0" class="progress-panel-section">
      <BuildProgressPanel
        :show-recent-tasks="true"
        :show-logs="false"
        :max-recent-tasks="3"
      />
    </div>

    &lt;!&ndash; 构建队列管理器 &ndash;&gt;
    <div v-if="showQueueManager" class="queue-manager-section">
      <BuildQueueManager
        ref="queueManager"
      />
    </div>-->
  </div>

    <!-- 快速构建 -->
  <div class="quick-build-section">
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">快速构建</h2>
        <div class="card-actions">
          <VCSelect
            v-model="quickBuildType"
            :options="buildTypeOptions"
            placeholder="构建类型"
            class="build-type-select"
          />
        </div>
      </div>
      <div class="card-content">
        <div class="platforms-grid">
          <div
            v-for="platform in platforms"
            :key="platform.id"
            class="platform-card"
            :class="{ 'platform-building': buildStore.isPlatformBuilding(platform.id) }"
          >
            <div class="platform-header">
              <div class="platform-icon-wrapper">
                <component :is="getPlatformIcon(platform.id)" class="w-6 h-6 platform-icon" />
              </div>
              <div class="platform-info">
                <h3 class="platform-name">{{ platform.name }}</h3>
                <p class="platform-description">{{ platform.description }}</p>
              </div>
              <div class="platform-status">
                <BuildStatusIndicator
                  :platform="platform.id"
                  size="sm"
                  :show-progress="false"
                  :show-duration="false"
                  :show-actions="false"
                />
              </div>
            </div>

            <div class="platform-stats">
              <div class="stat-item">
                <span class="stat-label">上次构建:</span>
                <span class="stat-value">{{ getLastBuildTime(platform.id) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">构建次数:</span>
                <span class="stat-value">{{ getBuildCount(platform.id) }}</span>
              </div>
            </div>

            <div class="platform-actions">
              <button
                @click="quickBuild(platform.id)"
                :disabled="buildStore.isPlatformBuilding(platform.id) || buildStore.isAnyBuilding"
                class="btn btn-primary btn-sm platform-build-btn"
                :class="{
                  'btn-loading': buildStore.isPlatformBuilding(platform.id),
                  'btn-disabled': buildStore.isAnyBuilding && !buildStore.isPlatformBuilding(platform.id)
                }"
                :title="getButtonDisabledReason(platform.id)"
              >
                <PlayIcon v-if="!buildStore.isPlatformBuilding(platform.id)" class="w-3 h-3 mr-1" />
                <div v-else class="w-3 h-3 mr-1 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {{ getPlatformButtonText(platform.id) }}
              </button>
              <button
                @click="showPlatformConfig(platform.id)"
                class="btn btn-ghost btn-sm"
              >
                <CogIcon class="w-3 h-3 mr-1" />
                配置
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 构建统计 -->
  <div class="build-stats-section">
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">构建统计</h2>
        <div class="card-actions">
          <VCSelect
            v-model="statsTimeRange"
            :options="timeRangeOptions"
            placeholder="时间范围"
            class="time-range-select"
          />
        </div>
      </div>
      <div class="card-content">
        <div v-if="isLoadingStats" class="loading-state">
          <div class="loading-spinner"></div>
          <p>加载统计信息...</p>
        </div>

        <div v-else class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <PlayIcon class="w-6 h-6 text-blue-500" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ buildStore.buildStats.totalBuilds || 0 }}</div>
              <div class="stat-label">总构建次数</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <CheckCircleIcon class="w-6 h-6 text-green-500" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ buildStore.buildStats.successfulBuilds || 0 }}</div>
              <div class="stat-label">成功构建</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <XCircleIcon class="w-6 h-6 text-red-500" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ buildStore.buildStats.failedBuilds || 0 }}</div>
              <div class="stat-label">失败构建</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <ClockIcon class="w-6 h-6 text-purple-500" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ buildStore.formatDuration(buildStore.buildStats.averageBuildTime || 0) }}</div>
              <div class="stat-label">平均耗时</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 构建历史 -->
  <div class="build-history-section">
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">构建历史</h2>
        <div class="card-actions">
          <VCSelect
            v-model="historyFilter"
            :options="historyFilterOptions"
            placeholder="筛选记录"
            class="filter-select"
          />
          <button
            @click="showBuildHistoryModal = true"
            class="btn btn-secondary btn-sm"
          >
            <EyeIcon class="w-4 h-4 mr-2" />
            查看全部
          </button>
        </div>
      </div>
      <div class="card-content">
        <div v-if="isLoadingHistory" class="loading-state">
          <div class="loading-spinner"></div>
          <p>加载构建历史...</p>
        </div>

        <div v-else-if="filteredBuildHistory.length === 0" class="empty-state">
          <ClockIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-500">暂无构建历史</p>
        </div>

        <div v-else class="history-timeline">
          <div
            v-for="(build, index) in filteredBuildHistory.slice(0, 10)"
            :key="build.id || index"
            class="timeline-item"
          >
            <div class="timeline-marker" :class="getTimelineMarkerClass(build)">
              <component :is="getBuildIcon(build)" class="w-4 h-4" />
            </div>
            <div class="timeline-content">
              <div class="timeline-header">
                <div class="build-summary">
                  <component :is="getPlatformIcon(build.platform)" class="w-4 h-4 mr-2" />
                  <span class="platform-name">{{ getPlatformName(build.platform) }}</span>
                  <ArrowRightIcon class="w-4 h-4 mx-2 text-gray-400" />
                  <span class="build-version">{{ build.version || '1.0.0' }}</span>
                </div>
                <div class="timeline-status">
                  <span :class="['status-badge', (build.success ?? (build.status === 'success')) ? 'status-success' : 'status-error']">
                    {{ (build.success ?? (build.status === 'success')) ? '成功' : '失败' }}
                  </span>
                </div>
              </div>
              <div class="timeline-details">
                <div class="build-meta">
                  <span class="timestamp">{{ formatTime(build.timestamp || (build.endTime ? build.endTime.toISOString() : '')) }}</span>
                  <span v-if="build.duration" class="duration">
                    耗时: {{ formatDuration(build.duration) }}
                  </span>
                  <span v-if="build.size" class="build-size">
                    大小: {{ formatSize(build.size) }}
                  </span>
                </div>
                <div v-if="build.error" class="error-message">
                  <ExclamationCircleIcon class="w-4 h-4 mr-1 text-red-500" />
                  {{ build.error }}
                </div>
              </div>
              <div class="timeline-actions">
                <button
                  @click="onViewBuildDetails(build)"
                  class="btn btn-sm btn-ghost"
                >
                  <EyeIcon class="w-3 h-3 mr-1" />
                  详情
                </button>
                <button
                  v-if="(build.success ?? (build.status === 'success')) && (build.artifactPath || build.buildPath)"
                  @click="downloadArtifact(build)"
                  class="btn btn-sm btn-ghost"
                >
                  <ArrowDownTrayIcon class="w-3 h-3 mr-1" />
                  下载
                </button>
                <button
                  v-if="build.success ?? (build.status === 'success')"
                  @click="rebuildVersion(build)"
                  class="btn btn-sm btn-ghost"
                >
                  <ArrowPathIcon class="w-3 h-3 mr-1" />
                  重新构建
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 模态框组件 -->
  <BuildConfigModal
    v-model="showBuildConfigModal"
    @save="onBuildConfigSaved"
  />

  <BuildModal
    v-model="showBuildModal"
    :platforms="platforms"
    @build="executeBuild"
  />

  <BuildHistoryModal
    v-model="showBuildHistoryModal"
    :build-history="buildStore.buildHistory"
    @view-details="onViewBuildDetails"
    @rebuild="onRebuildFromHistory"
  />

  <BuildDetailsModal
    v-model="showBuildDetailsModal"
    :build-record="selectedBuild"
    @rebuild="onRebuildFromDetails"
    @view-logs="onViewBuildLogs"
  />

  <BuildLogsModal
    v-model="showBuildLogsModal"
    :build-logs="selectedBuildLogs"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import {
  ArrowPathIcon,
  CogIcon,
  PlayIcon,
  EyeIcon,
  XMarkIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowRightIcon,
  ExclamationCircleIcon,
  ArrowDownTrayIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon,
  QueueListIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '@shared/api/VersionCraftAPIClient.ts';
import { ErrorHandler } from '@shared/services/ErrorHandler.ts';
import { NotificationService } from '@shared/services/NotificationService.ts';
import { useBuildStore, BuildStatus } from '../stores/build';

// 导入基础组件
import VCSelect from '../components/ui/VCSelect.vue';

// 导入构建组件
import BuildProgressPanel from '../components/build/BuildProgressPanel.vue';
import BuildStatusIndicator from '../components/build/BuildStatusIndicator.vue';
import BuildQueueManager from '../components/build/BuildQueueManager.vue';

// 导入模态框组件
import BuildConfigModal from '../components/modals/BuildConfigModal.vue';
import BuildModal from '../components/modals/BuildModal.vue';
import BuildHistoryModal from '../components/modals/BuildHistoryModal.vue';
import BuildDetailsModal from '../components/modals/BuildDetailsModal.vue';
import BuildLogsModal from '../components/modals/BuildLogsModal.vue';

// Store
const buildStore = useBuildStore();

// 响应式数据
const isLoading = ref(false);
const isLoadingStats = ref(false);
const isLoadingHistory = ref(false);
const quickBuildType = ref('debug');
const statsTimeRange = ref('week');
const historyFilter = ref('all');
const showQueueManager = ref(false);

// 组件引用
const queueManager = ref<InstanceType<typeof BuildQueueManager> | null>(null);

// 模态框状态
const showBuildModal = ref(false);
const showBuildConfigModal = ref(false);
const showBuildHistoryModal = ref(false);
const showBuildDetailsModal = ref(false);
const showBuildLogsModal = ref(false);

// 数据（使用 buildStore 中的数据）
const selectedBuild = ref<any>(null);
const selectedBuildLogs = ref<{
  buildId?: string;
  logs: Array<{
    timestamp: string;
    level: string;
    message: string;
    source?: string;
  }>;
} | undefined>(undefined);

// 轮询定时器
let buildPollingTimer: NodeJS.Timeout | null = null;

// 平台配置
const platforms = ref([
  {
    id: 'web-mobile',
    name: 'Web 移动端',
    description: '移动端 Web 应用',
    icon: ComputerDesktopIcon
  },
  {
    id: 'android',
    name: 'Android',
    description: 'Android 应用',
    icon: DevicePhoneMobileIcon
  },
  {
    id: 'ios',
    name: 'iOS',
    description: 'iOS 应用',
    icon: DeviceTabletIcon
  }
]);

// 计算属性
const buildTypeOptions = computed(() => [
  { label: '调试构建', value: 'debug' },
  { label: '发布构建', value: 'release' },
  { label: '测试构建', value: 'test' }
]);

const timeRangeOptions = computed(() => [
  { label: '最近一周', value: 'week' },
  { label: '最近一月', value: 'month' },
  { label: '最近三月', value: 'quarter' }
]);

const historyFilterOptions = computed(() => [
  { label: '全部记录', value: 'all' },
  { label: '成功构建', value: 'success' },
  { label: '失败构建', value: 'failed' },
  { label: 'Web 移动端', value: 'web-mobile' },
  { label: 'Android', value: 'android' },
  { label: 'iOS', value: 'ios' }
]);

const filteredBuildHistory = computed(() => {
  const history = buildStore.buildHistory || [];

  if (historyFilter.value === 'all') {
    return history;
  }

  return history.filter((build: any) => {
    switch (historyFilter.value) {
      case 'success':
        return build.success;
      case 'failed':
        return !build.success;
      case 'web-mobile':
      case 'android':
      case 'ios':
        return build.platform === historyFilter.value;
      default:
        return true;
    }
  });
});

// 生命周期
onMounted(async () => {
  await loadData();
  startBuildPolling();
});

onUnmounted(() => {
  stopBuildPolling();
});

// 方法
const loadData = async () => {
  await Promise.all([
    buildStore.loadBuildHistory(),
    buildStore.loadBuildStats()
  ]);
};

const refreshData = async () => {
  isLoading.value = true;
  await loadData();
  isLoading.value = false;
  NotificationService.success('数据已刷新');
};

const startBuildPolling = () => {
  // 每10秒轮询一次构建状态
  buildPollingTimer = setInterval(async () => {
    if (buildStore.isAnyBuilding) {
      // 构建状态现在通过事件监听器实时更新，无需轮询
      console.log('Build polling - active tasks:', buildStore.activeTasks.length);
    }
  }, 10000);
};

const stopBuildPolling = () => {
  if (buildPollingTimer) {
    clearInterval(buildPollingTimer);
    buildPollingTimer = null;
  }
};

// 构建相关方法
const quickBuild = async (platformId: string) => {
  try {
    // 检查是否已在构建
    if (buildStore.isPlatformBuilding(platformId)) {
      NotificationService.warning(`${buildStore.getPlatformName(platformId)} 正在构建中`);
      return;
    }

    // 检查是否有其他平台正在构建（防止并发构建导致系统卡死）
    if (buildStore.isAnyBuilding && buildStore.activeTasks.length >= 2) {
      NotificationService.warning('当前已有构建任务在进行中，请等待完成后再试');
      return;
    }

    // 防抖：检查最近是否刚启动过构建
    const lastBuildTime = localStorage.getItem(`lastBuild_${platformId}`);
    const now = Date.now();
    if (lastBuildTime && (now - parseInt(lastBuildTime)) < 3000) {
      NotificationService.warning('请勿频繁点击构建按钮');
      return;
    }

    // 记录构建时间
    localStorage.setItem(`lastBuild_${platformId}`, now.toString());

    // 启动构建
    await buildStore.startBuild(platformId, {
      configuration: quickBuildType.value as 'debug' | 'release'
    });

    NotificationService.success(`${buildStore.getPlatformName(platformId)} 构建已启动`);
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Quick Build');
  }
};

const executeBuild = async (buildOptions: any) => {
  try {
    // 检查是否有平台正在构建
    const buildingPlatforms = buildOptions.platforms.filter((platform: string) =>
      buildStore.isPlatformBuilding(platform)
    );

    if (buildingPlatforms.length > 0) {
      const platformNames = buildingPlatforms.map((p: string) => buildStore.getPlatformName(p)).join('、');
      NotificationService.warning(`${platformNames} 正在构建中，请等待完成后再试`);
      return;
    }

    // 检查并发构建限制
    const totalNewBuilds = buildOptions.platforms.length;
    const currentBuilds = buildStore.activeTasks.length;
    if (currentBuilds + totalNewBuilds > 3) {
      NotificationService.warning(`同时构建任务过多，当前 ${currentBuilds} 个，新增 ${totalNewBuilds} 个，最多允许 3 个并发构建`);
      return;
    }

    // 启动构建（逐个启动以避免系统过载）
    for (const platform of buildOptions.platforms) {
      await buildStore.startBuild(platform, {
        ...buildOptions,
        platform // 确保每个平台使用正确的配置
      });

      // 添加小延迟避免同时启动过多任务
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    NotificationService.success(`已启动 ${buildOptions.platforms.length} 个构建任务`);
    showBuildModal.value = false;
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Execute Build');
  }
};

const cancelBuild = async (buildId: string) => {
  try {
    const result = await apiClient.build.cancel(buildId);

    if (result.success) {
      NotificationService.success('构建任务已取消');
      // 构建状态通过事件监听器自动更新
    } else {
      throw new Error(result.error || '取消构建失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Cancel Build');
  }
};

const showBuildLogs = (build: any) => {
  selectedBuildLogs.value = {
    buildId: build.id,
    logs: build.logs || []
  };
  showBuildLogsModal.value = true;
};

// showBuildDetails 已替换为 onViewBuildDetails

const showPlatformConfig = (platformId: string) => {
  // 打开构建配置模态框，并切换到对应平台
  showBuildConfigModal.value = true;
  // 可以通过事件或其他方式通知模态框切换到指定平台
  NotificationService.info(`打开 ${getPlatformName(platformId)} 平台配置`);
};

const downloadArtifact = (build: any) => {
  if (build.artifactPath) {
    // 触发下载
    NotificationService.success('开始下载构建产物');
  }
};

const rebuildVersion = async (build: any) => {
  const confirmed = confirm(`确定要重新构建 ${build.platform} ${build.version} 吗？`);
  if (!confirmed) return;

  await quickBuild(build.platform);
};

// 事件处理
const onBuildConfigSaved = () => {
  NotificationService.success('构建配置已保存');
  loadData();
};

const onViewBuildDetails = (record: any) => {
  selectedBuild.value = record;
  showBuildDetailsModal.value = true;
};

const onRebuildFromHistory = async (record: any) => {
  try {
    await buildStore.startBuild(record.platform, {
      buildType: record.buildType || 'release',
      version: record.version
    });
    NotificationService.success(`开始重新构建 ${buildStore.getPlatformName(record.platform)}`);
  } catch (error) {
    console.error('Failed to rebuild from history:', error);
  }
};

const onRebuildFromDetails = async (record: any) => {
  showBuildDetailsModal.value = false;
  await onRebuildFromHistory(record);
};

const onViewBuildLogs = (record: any) => {
  selectedBuildLogs.value = {
    buildId: record.id,
    logs: record.logs || []
  };
  showBuildLogsModal.value = true;
};

// 工具方法（现在使用 buildStore.isPlatformBuilding）

const getPlatformIcon = (platform: string) => {
  const iconMap = {
    'web-mobile': ComputerDesktopIcon,
    'android': DevicePhoneMobileIcon,
    'ios': DeviceTabletIcon
  };
  return iconMap[platform as keyof typeof iconMap] || ComputerDesktopIcon;
};

const getPlatformName = (platform: string) => {
  const nameMap = {
    'web-mobile': 'Web 移动端',
    'android': 'Android',
    'ios': 'iOS'
  };
  return nameMap[platform as keyof typeof nameMap] || platform;
};

const getPlatformStatusClass = (_platformId: string) => {
  // 根据平台状态返回样式类
  return 'status-ready'; // 示例
};

const getLastBuildTime = (platformId: string) => {
  const lastBuild = buildStore.buildHistory.find((build: any) => build.platform === platformId);
  return lastBuild ? formatTime(lastBuild.timestamp || (lastBuild.endTime ? lastBuild.endTime.toISOString() : '')) : '从未构建';
};

const getBuildCount = (platformId: string) => {
  return buildStore.buildHistory.filter((build: any) => build.platform === platformId).length;
};

const getButtonDisabledReason = (platformId: string): string => {
  if (buildStore.isPlatformBuilding(platformId)) {
    return `${buildStore.getPlatformName(platformId)} 正在构建中`;
  }
  if (buildStore.isAnyBuilding) {
    const runningTasks = buildStore.activeTasks.map(task => buildStore.getPlatformName(task.platform)).join('、');
    return `${runningTasks} 正在构建中，请等待完成后再试`;
  }
  return '';
};

const getPlatformButtonText = (platformId: string): string => {
  const task = buildStore.buildTasks.get(platformId);
  if (!task) return '快速构建';

  switch (task.status) {
    case BuildStatus.PREPARING:
      return '准备中...';
    case BuildStatus.BUILDING:
      return '构建中...';
    default:
      return '快速构建';
  }
};

const getBuildTypeClass = (type: string) => {
  const classMap = {
    debug: 'type-debug',
    release: 'type-release',
    test: 'type-test'
  };
  return classMap[type as keyof typeof classMap] || 'type-debug';
};

const getBuildTypeText = (type: string) => {
  const textMap = {
    debug: '调试版本',
    release: '发布版本',
    test: '测试版本'
  };
  return textMap[type as keyof typeof textMap] || type;
};

const getTimelineMarkerClass = (build: any) => {
  return build.success ? 'timeline-marker-success' : 'timeline-marker-error';
};

const getBuildIcon = (build: any) => {
  return build.success ? CheckCircleIcon : XCircleIcon;
};

const formatElapsedTime = (startTime: string | Date) => {
  const now = new Date();
  const start = typeof startTime === 'string' ? new Date(startTime) : startTime;
  const elapsed = now.getTime() - start.getTime();

  return formatDuration(elapsed);
};

const formatTime = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diff = now.getTime() - time.getTime();

  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;

  return time.toLocaleString('zh-CN');
};

const formatDuration = (ms: number) => {
  if (!ms) return '-';

  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);

  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  }
  return `${seconds}秒`;
};

const formatSize = (bytes: number) => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};





</script>

<style scoped>
/* 构建管理页面样式 - 恢复标准布局，与其他页面保持一致 */
.build-management {
  @apply p-6 space-y-6 max-w-7xl mx-auto;
  min-height: calc(100vh - 2rem);
  background: #f8fafc;
}

/* 确保各个区域不重叠 */
.build-management > * {
  position: relative;
  z-index: 1;
}

/* 页面头部 */
.page-header {
  @apply bg-white rounded-lg shadow-sm border p-6;
}

.header-content {
  @apply mb-4;
}

.header-info {
  @apply mb-4;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
  display: block;
  white-space: normal;
}

.page-subtitle {
  @apply text-gray-600;
  display: block;
  white-space: normal;
}

.header-actions {
  @apply flex items-center space-x-3;
  flex-wrap: wrap;
  gap: 0.75rem;
}

/* 进度面板样式 - 动态显示时不占用额外空间 */
.progress-panel-section {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 队列管理器样式 */
.queue-manager-section {
  /* 使用标准的 space-y-6 间距 */
}

/* 快速构建样式 */
.quick-build-section {
  @apply space-y-4;
  /* 使用标准的 space-y-6 间距 */
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-lg shadow-sm border;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

.card-actions {
  @apply flex items-center space-x-3;
}

.card-content {
  @apply p-6;
}

/* 平台网格样式 */
.platforms-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.platform-card {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors;
}

.platform-card.platform-building {
  @apply border-blue-300 bg-blue-50;
}

.platform-header {
  @apply flex items-start justify-between mb-4;
}

.platform-icon-wrapper {
  @apply flex-shrink-0 mr-3;
}

.platform-icon {
  @apply text-gray-600;
}

.platform-info {
  @apply flex-1;
}

.platform-name {
  @apply text-lg font-semibold text-gray-900 mb-1;
}

.platform-description {
  @apply text-sm text-gray-600;
}

.platform-status {
  @apply flex-shrink-0;
}

.status-dot {
  @apply w-3 h-3 rounded-full;
}

.status-ready {
  @apply bg-green-400;
}

.status-building {
  @apply bg-blue-400;
}

.status-error {
  @apply bg-red-400;
}

.platform-stats {
  @apply space-y-2 mb-4;
}

.stat-item {
  @apply flex justify-between text-sm;
}

.stat-label {
  @apply text-gray-600;
}

.stat-value {
  @apply text-gray-900 font-medium;
}

.platform-actions {
  @apply flex space-x-2;
}

.platform-build-btn {
  @apply flex-1;
}

/* 构建历史样式 */
.history-timeline {
  @apply space-y-4;
}

.timeline-item {
  @apply flex items-start space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-200;
}

.timeline-marker {
  @apply flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center;
}

.timeline-marker.success {
  @apply bg-green-100 text-green-600;
}

.timeline-marker.failed {
  @apply bg-red-100 text-red-600;
}

.timeline-marker.running {
  @apply bg-blue-100 text-blue-600;
}

.timeline-content {
  @apply flex-1;
}

.timeline-header {
  @apply flex items-center justify-between mb-2;
}

.timeline-title {
  @apply font-semibold text-gray-900;
}

.timeline-time {
  @apply text-sm text-gray-500;
}

.timeline-details {
  @apply text-sm text-gray-600 space-y-1;
}

.timeline-actions {
  @apply flex-shrink-0 flex space-x-2;
}

/* 构建统计样式 */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.stat-card {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-200;
}

.stat-header {
  @apply flex items-center justify-between mb-2;
}

.stat-icon {
  @apply w-8 h-8 text-gray-600;
}

.stat-value {
  @apply text-2xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600;
}

.stat-change {
  @apply text-xs font-medium;
}

.stat-change.positive {
  @apply text-green-600;
}

.stat-change.negative {
  @apply text-red-600;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-primary:disabled {
  @apply bg-gray-400 cursor-not-allowed;
}

.btn-loading {
  @apply cursor-wait;
}

.btn-disabled {
  @apply bg-gray-300 text-gray-500 cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-ghost {
  @apply text-gray-600 hover:text-gray-800 hover:bg-gray-100;
}

/* 加载和空状态 */
.loading-state {
  @apply flex flex-col items-center justify-center py-12;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4;
}

.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .build-management {
    @apply p-4 space-y-4;
  }

  .header-content {
    @apply flex-col items-start space-y-4;
  }
}
</style>
