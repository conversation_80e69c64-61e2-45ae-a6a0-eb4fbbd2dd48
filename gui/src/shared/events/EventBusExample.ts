/**
 * 事件总线使用示例和测试工具
 * 展示如何正确使用新的事件总线系统
 */

import { mainEventBus } from '../../main/events/MainEventBus';
import { rendererEventBus } from './RendererEventBus';
import type { 
  BuildProgressEvent, 
  BuildCompleteEvent, 
  VersionChangedEvent 
} from '../../main/events/MainEventBus';

// ==================== 主进程使用示例 ====================

/**
 * 主进程中发布构建进度事件的示例
 */
export function examplePublishBuildProgress() {
  const buildProgressData: BuildProgressEvent = {
    buildId: 'build-123',
    platform: 'web-mobile',
    progress: 45,
    message: '正在编译资源...',
    elapsed: 30000,
    timestamp: new Date().toISOString()
  };

  // 直接通过事件总线发布
  mainEventBus.publishBuildProgress(buildProgressData);
  
  console.log('📡 [Example] Published build progress event');
}

/**
 * 主进程中发布构建完成事件的示例
 */
export function examplePublishBuildComplete() {
  const buildCompleteData: BuildCompleteEvent = {
    buildId: 'build-123',
    platform: 'web-mobile',
    success: true,
    timestamp: new Date().toISOString()
  };

  mainEventBus.publishBuildComplete(buildCompleteData);
  
  console.log('✅ [Example] Published build complete event');
}

/**
 * 主进程中发布版本变更事件的示例
 */
export function examplePublishVersionChanged() {
  const versionChangedData: VersionChangedEvent = {
    oldVersion: '1.0.0',
    newVersion: '1.1.0',
    type: 'minor',
    timestamp: new Date().toISOString()
  };

  mainEventBus.publishVersionChanged(versionChangedData);
  
  console.log('🏷️ [Example] Published version changed event');
}

// ==================== 渲染进程使用示例 ====================

/**
 * 渲染进程中订阅构建进度事件的示例
 */
export function exampleSubscribeBuildProgress() {
  const unsubscriber = rendererEventBus.subscribeBuildProgress((data: BuildProgressEvent) => {
    console.log('🎯 [Example] Received build progress:', data);
    
    // 更新 UI
    updateBuildProgressUI(data);
  });

  // 返回取消订阅函数，组件卸载时调用
  return unsubscriber;
}

/**
 * 渲染进程中订阅构建完成事件的示例
 */
export function exampleSubscribeBuildComplete() {
  const unsubscriber = rendererEventBus.subscribeBuildComplete((data: BuildCompleteEvent) => {
    console.log('✅ [Example] Received build complete:', data);
    
    // 更新 UI 和显示通知
    updateBuildCompleteUI(data);
    showBuildCompleteNotification(data);
  });

  return unsubscriber;
}

/**
 * 渲染进程中订阅版本变更事件的示例
 */
export function exampleSubscribeVersionChanged() {
  const unsubscriber = rendererEventBus.subscribeVersionChanged((data: VersionChangedEvent) => {
    console.log('🏷️ [Example] Received version changed:', data);
    
    // 刷新版本信息
    refreshVersionInfo(data);
  });

  return unsubscriber;
}

// ==================== Vue 组件中的使用示例 ====================

/**
 * Vue 组件中使用事件总线的示例
 */
export function exampleVueComponentUsage() {
  return `
// 在 Vue 组件的 setup() 函数中
import { onMounted, onUnmounted } from 'vue';
import { rendererEventBus } from '@/events/RendererEventBus';

export default {
  setup() {
    const unsubscribers: Array<() => void> = [];

    onMounted(() => {
      // 订阅构建进度事件
      const buildProgressUnsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
        console.log('构建进度更新:', data);
        // 更新组件状态
      });

      // 订阅构建完成事件
      const buildCompleteUnsubscriber = rendererEventBus.subscribeBuildComplete((data) => {
        console.log('构建完成:', data);
        // 显示完成通知
      });

      // 保存取消订阅函数
      unsubscribers.push(buildProgressUnsubscriber, buildCompleteUnsubscriber);
    });

    onUnmounted(() => {
      // 清理所有事件监听器
      unsubscribers.forEach(unsubscriber => unsubscriber());
    });

    return {
      // 组件的响应式数据和方法
    };
  }
};
  `;
}

// ==================== Pinia Store 中的使用示例 ====================

/**
 * Pinia Store 中使用事件总线的示例
 */
export function examplePiniaStoreUsage() {
  return `
// 在 Pinia Store 中
import { defineStore } from 'pinia';
import { ref, onUnmounted } from 'vue';
import { rendererEventBus } from '@/events/RendererEventBus';

export const useBuildStore = defineStore('build', () => {
  const buildProgress = ref(0);
  const buildStatus = ref('idle');
  const unsubscribers: Array<() => void> = [];

  // 设置事件监听器
  const setupEventListeners = () => {
    const progressUnsubscriber = rendererEventBus.subscribeBuildProgress((data) => {
      buildProgress.value = data.progress;
      buildStatus.value = 'building';
    });

    const completeUnsubscriber = rendererEventBus.subscribeBuildComplete((data) => {
      buildProgress.value = 100;
      buildStatus.value = data.success ? 'success' : 'failed';
    });

    unsubscribers.push(progressUnsubscriber, completeUnsubscriber);
  };

  // 清理事件监听器
  const cleanup = () => {
    unsubscribers.forEach(unsubscriber => unsubscriber());
    unsubscribers.length = 0;
  };

  // 自动设置监听器
  setupEventListeners();

  // 组件卸载时清理
  onUnmounted(cleanup);

  return {
    buildProgress,
    buildStatus,
    cleanup
  };
});
  `;
}

// ==================== 工具函数 ====================

function updateBuildProgressUI(data: BuildProgressEvent) {
  // 模拟更新 UI 的逻辑
  console.log(`更新构建进度 UI: ${data.platform} - ${data.progress}%`);
}

function updateBuildCompleteUI(data: BuildCompleteEvent) {
  // 模拟更新完成状态 UI 的逻辑
  console.log(`更新构建完成 UI: ${data.platform} - ${data.success ? '成功' : '失败'}`);
}

function showBuildCompleteNotification(data: BuildCompleteEvent) {
  // 模拟显示通知的逻辑
  console.log(`显示通知: 构建${data.success ? '成功' : '失败'} - ${data.platform}`);
}

function refreshVersionInfo(data: VersionChangedEvent) {
  // 模拟刷新版本信息的逻辑
  console.log(`刷新版本信息: ${data.oldVersion} -> ${data.newVersion}`);
}

// ==================== 调试和测试工具 ====================

/**
 * 测试事件总线功能
 */
export function testEventBus() {
  console.log('🧪 [EventBus] Starting event bus test...');

  // 测试主进程事件发布
  setTimeout(() => {
    examplePublishBuildProgress();
  }, 1000);

  setTimeout(() => {
    examplePublishBuildComplete();
  }, 2000);

  setTimeout(() => {
    examplePublishVersionChanged();
  }, 3000);

  console.log('✅ [EventBus] Event bus test scheduled');
}

/**
 * 获取事件总线统计信息
 */
export function getEventBusStats() {
  const mainStats = mainEventBus.getEventStats();
  const rendererStats = rendererEventBus.getEventStats();
  const subscriptionStats = rendererEventBus.getSubscriptionStats();

  return {
    main: Object.fromEntries(mainStats),
    renderer: Object.fromEntries(rendererStats),
    subscriptions: Object.fromEntries(subscriptionStats)
  };
}
